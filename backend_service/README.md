# Quiz Backend Service

## 项目概述
基于Java Servlet的QUIZ心理测评系统后台服务，提供RESTful API接口。

## 技术栈
- Java 8
- Servlet 4.0
- MySQL 8.0
- JWT认证
- BCrypt密码加密
- Gson JSON处理

## 项目结构
```
backend_service/
├── src/main/java/com/quiz/
│   ├── dao/           # 数据访问层
│   ├── filter/        # 过滤器
│   ├── model/         # 实体类
│   ├── servlet/       # Servlet控制器
│   └── util/          # 工具类
├── src/main/webapp/
│   └── WEB-INF/
│       └── web.xml    # Web配置
└── pom.xml           # Maven配置
```

## API接口

### 认证相关 (/api/auth/*)
- POST /api/auth/register - 用户注册
- POST /api/auth/login - 用户登录
- POST /api/auth/refresh - 刷新token

### 用户相关 (/api/user/*)
- GET /api/user/profile - 获取用户信息
- PUT /api/user/profile - 更新用户信息

### 测评相关 (/api/quiz/*)
- GET /api/quiz/list - 获取测评列表
- GET /api/quiz/{id} - 获取测评详情
- GET /api/quiz/{id}/questions - 获取测评题目

### 答题相关 (/api/answer/*)
- POST /api/answer/submit - 提交答案
- GET /api/answer/result/{recordId} - 获取测评结果

## 数据库配置
- 数据库名: quiz
- 用户名: root
- 密码: SD0916sd!

## 构建和运行

### 编译项目
```bash
mvn clean compile
```

### 打包WAR文件
```bash
mvn clean package
```

### 本地开发运行
```bash
mvn tomcat7:run
```

## 部署说明
1. 确保MySQL服务运行并创建quiz数据库
2. 执行database/init.sql初始化数据表
3. 将生成的WAR文件部署到Tomcat服务器
4. 访问 http://localhost:8080/quiz-api 测试接口

## 待完成功能
- [ ] 测评管理API完整实现
- [ ] 答题逻辑和结果计算
- [ ] 管理后台API
- [ ] 文件上传功能
- [ ] 数据统计分析
- [ ] 缓存机制
- [ ] 日志记录
- [ ] 单元测试
