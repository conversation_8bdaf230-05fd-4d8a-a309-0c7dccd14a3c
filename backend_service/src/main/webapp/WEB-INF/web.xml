<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee
         http://xmlns.jcp.org/xml/ns/javaee/web-app_4_0.xsd"
         version="4.0">

    <display-name>Quiz Backend Service</display-name>
    <description>QUIZ心理测评系统后台服务</description>

    <!-- 数据库初始化监听器 -->
    <listener>
        <listener-class>com.quiz.listener.DatabaseInitListener</listener-class>
    </listener>

    <!-- 数据库连接池配置 -->
    <context-param>
        <param-name>db.driver</param-name>
        <param-value>com.mysql.cj.jdbc.Driver</param-value>
    </context-param>
    <context-param>
        <param-name>db.url</param-name>
        <param-value>*****************************************************************************************************************************</param-value>
    </context-param>
    <context-param>
        <param-name>db.username</param-name>
        <param-value>root</param-value>
    </context-param>
    <context-param>
        <param-name>db.password</param-name>
        <param-value>SD0916sd!</param-value>
    </context-param>

    <!-- 跨域过滤器 -->
    <filter>
        <filter-name>CorsFilter</filter-name>
        <filter-class>com.quiz.filter.CorsFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>CorsFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- 认证过滤器 -->
    <filter>
        <filter-name>AuthFilter</filter-name>
        <filter-class>com.quiz.filter.AuthFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>AuthFilter</filter-name>
        <url-pattern>/api/user/*</url-pattern>
        <url-pattern>/api/quiz/take/*</url-pattern>
        <url-pattern>/api/result/*</url-pattern>
    </filter-mapping>

    <!-- 用户认证相关Servlet -->
    <servlet>
        <servlet-name>AuthServlet</servlet-name>
        <servlet-class>com.quiz.servlet.AuthServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AuthServlet</servlet-name>
        <url-pattern>/api/auth/*</url-pattern>
    </servlet-mapping>

    <!-- 用户管理Servlet -->
    <servlet>
        <servlet-name>UserServlet</servlet-name>
        <servlet-class>com.quiz.servlet.UserServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>UserServlet</servlet-name>
        <url-pattern>/api/user/*</url-pattern>
    </servlet-mapping>

    <!-- 测评管理Servlet -->
    <servlet>
        <servlet-name>QuizServlet</servlet-name>
        <servlet-class>com.quiz.servlet.QuizServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>QuizServlet</servlet-name>
        <url-pattern>/api/quiz/*</url-pattern>
    </servlet-mapping>

    <!-- 答题相关Servlet -->
    <servlet>
        <servlet-name>AnswerServlet</servlet-name>
        <servlet-class>com.quiz.servlet.AnswerServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AnswerServlet</servlet-name>
        <url-pattern>/api/answer/*</url-pattern>
    </servlet-mapping>

    <!-- 结果相关Servlet -->
    <servlet>
        <servlet-name>ResultServlet</servlet-name>
        <servlet-class>com.quiz.servlet.ResultServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ResultServlet</servlet-name>
        <url-pattern>/api/result/*</url-pattern>
    </servlet-mapping>

    <!-- 管理后台Servlet -->
    <servlet>
        <servlet-name>AdminServlet</servlet-name>
        <servlet-class>com.quiz.servlet.AdminServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AdminServlet</servlet-name>
        <url-pattern>/api/admin/*</url-pattern>
    </servlet-mapping>

    <!-- 欢迎页面 -->
    <welcome-file-list>
        <welcome-file>index.html</welcome-file>
    </welcome-file-list>

    <!-- 错误页面配置 -->
    <error-page>
        <error-code>404</error-code>
        <location>/error/404.html</location>
    </error-page>
    <error-page>
        <error-code>500</error-code>
        <location>/error/500.html</location>
    </error-page>

    <!-- 会话配置 -->
    <session-config>
        <session-timeout>30</session-timeout>
    </session-config>

</web-app>
