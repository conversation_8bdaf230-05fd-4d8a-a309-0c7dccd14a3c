package com.quiz.util;

import at.favre.lib.crypto.bcrypt.BCrypt;

/**
 * 密码工具类
 * 用于密码加密和验证
 */
public class PasswordUtil {
    
    /**
     * 加密密码
     */
    public static String hashPassword(String password) {
        return BCrypt.withDefaults().hashToString(12, password.toCharArray());
    }
    
    /**
     * 验证密码
     */
    public static boolean verifyPassword(String password, String hashedPassword) {
        BCrypt.Result result = BCrypt.verifyer().verify(password.toCharArray(), hashedPassword);
        return result.verified;
    }
}
