package com.quiz.util;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonSyntaxException;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;

/**
 * JSON工具类
 * 用于处理JSON序列化和反序列化
 */
public class JsonUtil {
    private static final Gson gson = new GsonBuilder()
            .setDateFormat("yyyy-MM-dd HH:mm:ss")
            .create();
    
    /**
     * 对象转JSON字符串
     */
    public static String toJson(Object obj) {
        return gson.toJson(obj);
    }
    
    /**
     * JSON字符串转对象
     */
    public static <T> T fromJson(String json, Class<T> clazz) throws JsonSyntaxException {
        return gson.fromJson(json, clazz);
    }
    
    /**
     * 创建成功响应
     */
    public static Map<String, Object> success(Object data) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "success");
        result.put("data", data);
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }
    
    /**
     * 创建成功响应（无数据）
     */
    public static Map<String, Object> success() {
        return success(null);
    }
    
    /**
     * 创建错误响应
     */
    public static Map<String, Object> error(int code, String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", code);
        result.put("message", message);
        result.put("data", null);
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }
    
    /**
     * 创建错误响应（默认500错误）
     */
    public static Map<String, Object> error(String message) {
        return error(500, message);
    }
    
    /**
     * 发送JSON响应
     */
    public static void sendJsonResponse(HttpServletResponse response, Object data) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        
        PrintWriter out = response.getWriter();
        out.print(toJson(data));
        out.flush();
    }
    
    /**
     * 发送成功响应
     */
    public static void sendSuccess(HttpServletResponse response, Object data) throws IOException {
        sendJsonResponse(response, success(data));
    }
    
    /**
     * 发送成功响应（无数据）
     */
    public static void sendSuccess(HttpServletResponse response) throws IOException {
        sendJsonResponse(response, success());
    }
    
    /**
     * 发送错误响应
     */
    public static void sendError(HttpServletResponse response, int code, String message) throws IOException {
        response.setStatus(code);
        sendJsonResponse(response, error(code, message));
    }
    
    /**
     * 发送错误响应（默认500错误）
     */
    public static void sendError(HttpServletResponse response, String message) throws IOException {
        sendError(response, 500, message);
    }
    
    /**
     * 发送400错误（请求参数错误）
     */
    public static void sendBadRequest(HttpServletResponse response, String message) throws IOException {
        sendError(response, 400, message);
    }
    
    /**
     * 发送401错误（未授权）
     */
    public static void sendUnauthorized(HttpServletResponse response, String message) throws IOException {
        sendError(response, 401, message);
    }
    
    /**
     * 发送403错误（禁止访问）
     */
    public static void sendForbidden(HttpServletResponse response, String message) throws IOException {
        sendError(response, 403, message);
    }
    
    /**
     * 发送404错误（资源不存在）
     */
    public static void sendNotFound(HttpServletResponse response, String message) throws IOException {
        sendError(response, 404, message);
    }
}
