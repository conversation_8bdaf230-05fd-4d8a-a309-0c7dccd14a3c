package com.quiz.util;

import javax.servlet.ServletContext;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * 数据库连接池工具类
 * 简单的数据库连接池实现
 */
public class DatabaseUtil {
    private static DatabaseUtil instance;
    private BlockingQueue<Connection> connectionPool;
    private String url;
    private String username;
    private String password;
    private String driver;
    
    private static final int INITIAL_POOL_SIZE = 10;
    private static final int MAX_POOL_SIZE = 20;
    
    private DatabaseUtil() {
        connectionPool = new LinkedBlockingQueue<>();
    }
    
    public static synchronized DatabaseUtil getInstance() {
        if (instance == null) {
            instance = new DatabaseUtil();
        }
        return instance;
    }
    
    /**
     * 初始化数据库连接池
     */
    public void initialize(ServletContext context) throws SQLException, ClassNotFoundException {
        this.driver = context.getInitParameter("db.driver");
        this.url = context.getInitParameter("db.url");
        this.username = context.getInitParameter("db.username");
        this.password = context.getInitParameter("db.password");
        
        // 加载数据库驱动
        Class.forName(driver);
        
        // 创建初始连接
        for (int i = 0; i < INITIAL_POOL_SIZE; i++) {
            Connection conn = createConnection();
            connectionPool.offer(conn);
        }
    }
    
    /**
     * 创建新的数据库连接
     */
    private Connection createConnection() throws SQLException {
        return DriverManager.getConnection(url, username, password);
    }
    
    /**
     * 获取数据库连接
     */
    public Connection getConnection() throws SQLException {
        Connection conn = connectionPool.poll();
        if (conn == null || conn.isClosed()) {
            // 如果池中没有可用连接或连接已关闭，创建新连接
            if (connectionPool.size() < MAX_POOL_SIZE) {
                conn = createConnection();
            } else {
                // 等待可用连接
                try {
                    conn = connectionPool.take();
                    if (conn.isClosed()) {
                        conn = createConnection();
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new SQLException("获取数据库连接被中断", e);
                }
            }
        }
        return conn;
    }
    
    /**
     * 归还数据库连接到池中
     */
    public void returnConnection(Connection conn) {
        if (conn != null) {
            try {
                if (!conn.isClosed()) {
                    connectionPool.offer(conn);
                }
            } catch (SQLException e) {
                // 连接有问题，不归还到池中
                try {
                    conn.close();
                } catch (SQLException ex) {
                    // 忽略关闭异常
                }
            }
        }
    }
    
    /**
     * 关闭连接池
     */
    public void closePool() {
        while (!connectionPool.isEmpty()) {
            Connection conn = connectionPool.poll();
            try {
                if (conn != null && !conn.isClosed()) {
                    conn.close();
                }
            } catch (SQLException e) {
                // 忽略关闭异常
            }
        }
    }
    
    /**
     * 获取连接池状态信息
     */
    public String getPoolStatus() {
        return String.format("连接池状态 - 可用连接数: %d, 最大连接数: %d", 
                           connectionPool.size(), MAX_POOL_SIZE);
    }
}
