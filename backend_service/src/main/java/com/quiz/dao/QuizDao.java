package com.quiz.dao;

import com.quiz.model.Quiz;
import com.quiz.model.Question;
import com.quiz.model.QuestionOption;
import com.quiz.util.DatabaseUtil;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 测评数据访问对象
 */
public class QuizDao {
    
    /**
     * 获取测评列表
     */
    public List<Quiz> getQuizzes(Integer categoryId, Boolean isFeatured, int offset, int limit) throws SQLException {
        StringBuilder sql = new StringBuilder("SELECT q.*, c.name as category_name FROM quizzes q ");
        sql.append("LEFT JOIN quiz_categories c ON q.category_id = c.id ");
        sql.append("WHERE q.status = 1 ");
        
        List<Object> params = new ArrayList<>();
        
        if (categoryId != null) {
            sql.append("AND q.category_id = ? ");
            params.add(categoryId);
        }
        
        if (isFeatured != null) {
            sql.append("AND q.is_featured = ? ");
            params.add(isFeatured);
        }
        
        sql.append("ORDER BY q.created_at DESC LIMIT ? OFFSET ?");
        params.add(limit);
        params.add(offset);
        
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getInstance().getConnection();
            stmt = conn.prepareStatement(sql.toString());
            
            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }
            
            rs = stmt.executeQuery();
            List<Quiz> quizzes = new ArrayList<>();
            
            while (rs.next()) {
                quizzes.add(mapResultSetToQuiz(rs));
            }
            
            return quizzes;
        } finally {
            closeResources(rs, stmt, conn);
        }
    }
    
    /**
     * 根据ID获取测评详情
     */
    public Quiz getQuizById(Long id) throws SQLException {
        String sql = "SELECT q.*, c.name as category_name FROM quizzes q " +
                    "LEFT JOIN quiz_categories c ON q.category_id = c.id " +
                    "WHERE q.id = ? AND q.status = 1";
        
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getInstance().getConnection();
            stmt = conn.prepareStatement(sql);
            stmt.setLong(1, id);
            rs = stmt.executeQuery();
            
            if (rs.next()) {
                return mapResultSetToQuiz(rs);
            }
            return null;
        } finally {
            closeResources(rs, stmt, conn);
        }
    }
    
    /**
     * 获取测评的题目列表
     */
    public List<Question> getQuizQuestions(Long quizId) throws SQLException {
        String sql = "SELECT * FROM questions WHERE quiz_id = ? ORDER BY sort_order, id";
        
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getInstance().getConnection();
            stmt = conn.prepareStatement(sql);
            stmt.setLong(1, quizId);
            rs = stmt.executeQuery();
            
            List<Question> questions = new ArrayList<>();
            while (rs.next()) {
                Question question = mapResultSetToQuestion(rs);
                // 获取题目选项
                question.setOptions(getQuestionOptions(question.getId()));
                questions.add(question);
            }
            
            return questions;
        } finally {
            closeResources(rs, stmt, conn);
        }
    }
    
    /**
     * 获取题目选项
     */
    public List<QuestionOption> getQuestionOptions(Long questionId) throws SQLException {
        String sql = "SELECT * FROM question_options WHERE question_id = ? ORDER BY sort_order, id";
        
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getInstance().getConnection();
            stmt = conn.prepareStatement(sql);
            stmt.setLong(1, questionId);
            rs = stmt.executeQuery();
            
            List<QuestionOption> options = new ArrayList<>();
            while (rs.next()) {
                options.add(mapResultSetToQuestionOption(rs));
            }
            
            return options;
        } finally {
            closeResources(rs, stmt, conn);
        }
    }
    
    /**
     * 增加测评浏览次数
     */
    public void incrementViewCount(Long quizId) throws SQLException {
        String sql = "UPDATE quizzes SET view_count = view_count + 1 WHERE id = ?";
        
        Connection conn = null;
        PreparedStatement stmt = null;
        
        try {
            conn = DatabaseUtil.getInstance().getConnection();
            stmt = conn.prepareStatement(sql);
            stmt.setLong(1, quizId);
            stmt.executeUpdate();
        } finally {
            closeResources(null, stmt, conn);
        }
    }
    
    /**
     * 增加测评参与次数
     */
    public void incrementTakeCount(Long quizId) throws SQLException {
        String sql = "UPDATE quizzes SET take_count = take_count + 1 WHERE id = ?";
        
        Connection conn = null;
        PreparedStatement stmt = null;
        
        try {
            conn = DatabaseUtil.getInstance().getConnection();
            stmt = conn.prepareStatement(sql);
            stmt.setLong(1, quizId);
            stmt.executeUpdate();
        } finally {
            closeResources(null, stmt, conn);
        }
    }
    
    /**
     * 将ResultSet映射为Quiz对象
     */
    private Quiz mapResultSetToQuiz(ResultSet rs) throws SQLException {
        Quiz quiz = new Quiz();
        quiz.setId(rs.getLong("id"));
        quiz.setTitle(rs.getString("title"));
        quiz.setSubtitle(rs.getString("subtitle"));
        quiz.setDescription(rs.getString("description"));
        quiz.setCategoryId(rs.getObject("category_id", Integer.class));
        quiz.setCategoryName(rs.getString("category_name"));
        quiz.setCoverImage(rs.getString("cover_image"));
        quiz.setQuestionCount(rs.getInt("question_count"));
        quiz.setEstimatedTime(rs.getInt("estimated_time"));
        quiz.setDifficultyLevel(rs.getInt("difficulty_level"));
        quiz.setIsFree(rs.getBoolean("is_free"));
        quiz.setPrice(rs.getBigDecimal("price"));
        quiz.setIsFeatured(rs.getBoolean("is_featured"));
        quiz.setViewCount(rs.getLong("view_count"));
        quiz.setTakeCount(rs.getLong("take_count"));
        quiz.setStatus(rs.getInt("status"));
        quiz.setCreatedAt(rs.getTimestamp("created_at"));
        quiz.setUpdatedAt(rs.getTimestamp("updated_at"));
        return quiz;
    }
    
    /**
     * 将ResultSet映射为Question对象
     */
    private Question mapResultSetToQuestion(ResultSet rs) throws SQLException {
        Question question = new Question();
        question.setId(rs.getLong("id"));
        question.setQuizId(rs.getLong("quiz_id"));
        question.setQuestionText(rs.getString("question_text"));
        question.setQuestionType(rs.getInt("question_type"));
        question.setImageUrl(rs.getString("image_url"));
        question.setSortOrder(rs.getInt("sort_order"));
        question.setIsRequired(rs.getBoolean("is_required"));
        question.setCreatedAt(rs.getTimestamp("created_at"));
        question.setUpdatedAt(rs.getTimestamp("updated_at"));
        return question;
    }
    
    /**
     * 将ResultSet映射为QuestionOption对象
     */
    private QuestionOption mapResultSetToQuestionOption(ResultSet rs) throws SQLException {
        QuestionOption option = new QuestionOption();
        option.setId(rs.getLong("id"));
        option.setQuestionId(rs.getLong("question_id"));
        option.setOptionText(rs.getString("option_text"));
        option.setOptionValue(rs.getString("option_value"));
        option.setScore(rs.getInt("score"));
        option.setSortOrder(rs.getInt("sort_order"));
        option.setCreatedAt(rs.getTimestamp("created_at"));
        return option;
    }
    
    /**
     * 关闭数据库资源
     */
    private void closeResources(ResultSet rs, PreparedStatement stmt, Connection conn) {
        try {
            if (rs != null) rs.close();
            if (stmt != null) stmt.close();
            if (conn != null) DatabaseUtil.getInstance().returnConnection(conn);
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
}
