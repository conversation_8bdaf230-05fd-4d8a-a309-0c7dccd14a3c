package com.quiz.dao;

import com.quiz.model.UserQuizRecord;
import com.quiz.model.UserAnswer;
import com.quiz.util.DatabaseUtil;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户测评记录数据访问对象
 */
public class UserQuizRecordDao {
    
    /**
     * 创建测评记录
     */
    public Long createRecord(UserQuizRecord record) throws SQLException {
        String sql = "INSERT INTO user_quiz_records (user_id, quiz_id, start_time, total_score, status) " +
                    "VALUES (?, ?, CURRENT_TIMESTAMP, ?, ?)";
        
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getInstance().getConnection();
            stmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            stmt.setLong(1, record.getUserId());
            stmt.setLong(2, record.getQuizId());
            stmt.setInt(3, record.getTotalScore());
            stmt.setInt(4, record.getStatus());
            
            int affectedRows = stmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("创建测评记录失败，没有行被影响");
            }
            
            rs = stmt.getGeneratedKeys();
            if (rs.next()) {
                return rs.getLong(1);
            } else {
                throw new SQLException("创建测评记录失败，无法获取ID");
            }
        } finally {
            closeResources(rs, stmt, conn);
        }
    }
    
    /**
     * 根据ID获取测评记录
     */
    public UserQuizRecord getRecordById(Long id) throws SQLException {
        String sql = "SELECT r.*, q.title as quiz_title FROM user_quiz_records r " +
                    "LEFT JOIN quizzes q ON r.quiz_id = q.id " +
                    "WHERE r.id = ?";
        
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getInstance().getConnection();
            stmt = conn.prepareStatement(sql);
            stmt.setLong(1, id);
            rs = stmt.executeQuery();
            
            if (rs.next()) {
                return mapResultSetToRecord(rs);
            }
            return null;
        } finally {
            closeResources(rs, stmt, conn);
        }
    }
    
    /**
     * 获取用户的测评记录列表
     */
    public List<UserQuizRecord> getUserRecords(Long userId, int offset, int limit) throws SQLException {
        String sql = "SELECT r.*, q.title as quiz_title FROM user_quiz_records r " +
                    "LEFT JOIN quizzes q ON r.quiz_id = q.id " +
                    "WHERE r.user_id = ? ORDER BY r.created_at DESC LIMIT ? OFFSET ?";
        
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getInstance().getConnection();
            stmt = conn.prepareStatement(sql);
            stmt.setLong(1, userId);
            stmt.setInt(2, limit);
            stmt.setInt(3, offset);
            rs = stmt.executeQuery();
            
            List<UserQuizRecord> records = new ArrayList<>();
            while (rs.next()) {
                records.add(mapResultSetToRecord(rs));
            }
            
            return records;
        } finally {
            closeResources(rs, stmt, conn);
        }
    }
    
    /**
     * 更新测评记录
     */
    public boolean updateRecord(UserQuizRecord record) throws SQLException {
        String sql = "UPDATE user_quiz_records SET end_time = ?, total_score = ?, " +
                    "result_type = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        
        Connection conn = null;
        PreparedStatement stmt = null;
        
        try {
            conn = DatabaseUtil.getInstance().getConnection();
            stmt = conn.prepareStatement(sql);
            stmt.setTimestamp(1, record.getEndTime());
            stmt.setInt(2, record.getTotalScore());
            stmt.setString(3, record.getResultType());
            stmt.setInt(4, record.getStatus());
            stmt.setLong(5, record.getId());
            
            return stmt.executeUpdate() > 0;
        } finally {
            closeResources(null, stmt, conn);
        }
    }
    
    /**
     * 保存用户答案
     */
    public Long saveAnswer(UserAnswer answer) throws SQLException {
        String sql = "INSERT INTO user_answers (record_id, question_id, option_id, answer_text, score) " +
                    "VALUES (?, ?, ?, ?, ?)";
        
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getInstance().getConnection();
            stmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            stmt.setLong(1, answer.getRecordId());
            stmt.setLong(2, answer.getQuestionId());
            stmt.setObject(3, answer.getOptionId());
            stmt.setString(4, answer.getAnswerText());
            stmt.setInt(5, answer.getScore());
            
            int affectedRows = stmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("保存答案失败，没有行被影响");
            }
            
            rs = stmt.getGeneratedKeys();
            if (rs.next()) {
                return rs.getLong(1);
            } else {
                throw new SQLException("保存答案失败，无法获取ID");
            }
        } finally {
            closeResources(rs, stmt, conn);
        }
    }
    
    /**
     * 获取测评记录的所有答案
     */
    public List<UserAnswer> getRecordAnswers(Long recordId) throws SQLException {
        String sql = "SELECT * FROM user_answers WHERE record_id = ? ORDER BY question_id";
        
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getInstance().getConnection();
            stmt = conn.prepareStatement(sql);
            stmt.setLong(1, recordId);
            rs = stmt.executeQuery();
            
            List<UserAnswer> answers = new ArrayList<>();
            while (rs.next()) {
                answers.add(mapResultSetToAnswer(rs));
            }
            
            return answers;
        } finally {
            closeResources(rs, stmt, conn);
        }
    }
    
    /**
     * 将ResultSet映射为UserQuizRecord对象
     */
    private UserQuizRecord mapResultSetToRecord(ResultSet rs) throws SQLException {
        UserQuizRecord record = new UserQuizRecord();
        record.setId(rs.getLong("id"));
        record.setUserId(rs.getLong("user_id"));
        record.setQuizId(rs.getLong("quiz_id"));
        record.setQuizTitle(rs.getString("quiz_title"));
        record.setStartTime(rs.getTimestamp("start_time"));
        record.setEndTime(rs.getTimestamp("end_time"));
        record.setTotalScore(rs.getInt("total_score"));
        record.setResultType(rs.getString("result_type"));
        record.setStatus(rs.getInt("status"));
        record.setCreatedAt(rs.getTimestamp("created_at"));
        record.setUpdatedAt(rs.getTimestamp("updated_at"));
        return record;
    }
    
    /**
     * 将ResultSet映射为UserAnswer对象
     */
    private UserAnswer mapResultSetToAnswer(ResultSet rs) throws SQLException {
        UserAnswer answer = new UserAnswer();
        answer.setId(rs.getLong("id"));
        answer.setRecordId(rs.getLong("record_id"));
        answer.setQuestionId(rs.getLong("question_id"));
        answer.setOptionId(rs.getObject("option_id", Long.class));
        answer.setAnswerText(rs.getString("answer_text"));
        answer.setScore(rs.getInt("score"));
        answer.setCreatedAt(rs.getTimestamp("created_at"));
        return answer;
    }
    
    /**
     * 关闭数据库资源
     */
    private void closeResources(ResultSet rs, PreparedStatement stmt, Connection conn) {
        try {
            if (rs != null) rs.close();
            if (stmt != null) stmt.close();
            if (conn != null) DatabaseUtil.getInstance().returnConnection(conn);
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
}
