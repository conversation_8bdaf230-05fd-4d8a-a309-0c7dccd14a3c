package com.quiz.listener;

import com.quiz.util.DatabaseUtil;

import javax.servlet.ServletContext;
import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import java.sql.SQLException;

/**
 * 数据库初始化监听器
 * 在应用启动时初始化数据库连接池
 */
public class DatabaseInitListener implements ServletContextListener {
    
    @Override
    public void contextInitialized(ServletContextEvent sce) {
        ServletContext context = sce.getServletContext();
        
        try {
            // 初始化数据库连接池
            DatabaseUtil.getInstance().initialize(context);
            System.out.println("数据库连接池初始化成功");
        } catch (SQLException | ClassNotFoundException e) {
            System.err.println("数据库连接池初始化失败: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("数据库连接池初始化失败", e);
        }
    }
    
    @Override
    public void contextDestroyed(ServletContextEvent sce) {
        try {
            // 关闭数据库连接池
            DatabaseUtil.getInstance().closePool();
            System.out.println("数据库连接池已关闭");
        } catch (Exception e) {
            System.err.println("关闭数据库连接池时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
