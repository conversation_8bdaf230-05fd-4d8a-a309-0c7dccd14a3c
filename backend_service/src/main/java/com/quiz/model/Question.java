package com.quiz.model;

import java.sql.Timestamp;
import java.util.List;

/**
 * 题目实体类
 */
public class Question {
    private Long id;
    private Long quizId;
    private String questionText;
    private Integer questionType; // 1-单选, 2-多选, 3-滑动打分
    private String imageUrl;
    private Integer sortOrder;
    private Boolean isRequired;
    private Timestamp createdAt;
    private Timestamp updatedAt;
    
    // 关联的选项列表
    private List<QuestionOption> options;

    // 构造函数
    public Question() {}

    public Question(Long quizId, String questionText, Integer questionType) {
        this.quizId = quizId;
        this.questionText = questionText;
        this.questionType = questionType;
        this.isRequired = true;
        this.sortOrder = 0;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getQuizId() {
        return quizId;
    }

    public void setQuizId(Long quizId) {
        this.quizId = quizId;
    }

    public String getQuestionText() {
        return questionText;
    }

    public void setQuestionText(String questionText) {
        this.questionText = questionText;
    }

    public Integer getQuestionType() {
        return questionType;
    }

    public void setQuestionType(Integer questionType) {
        this.questionType = questionType;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Boolean getIsRequired() {
        return isRequired;
    }

    public void setIsRequired(Boolean isRequired) {
        this.isRequired = isRequired;
    }

    public Timestamp getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Timestamp createdAt) {
        this.createdAt = createdAt;
    }

    public Timestamp getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Timestamp updatedAt) {
        this.updatedAt = updatedAt;
    }

    public List<QuestionOption> getOptions() {
        return options;
    }

    public void setOptions(List<QuestionOption> options) {
        this.options = options;
    }

    @Override
    public String toString() {
        return "Question{" +
                "id=" + id +
                ", quizId=" + quizId +
                ", questionText='" + questionText + '\'' +
                ", questionType=" + questionType +
                ", sortOrder=" + sortOrder +
                '}';
    }
}
