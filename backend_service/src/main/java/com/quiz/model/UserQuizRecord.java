package com.quiz.model;

import java.sql.Timestamp;

/**
 * 用户测评记录实体类
 */
public class UserQuizRecord {
    private Long id;
    private Long userId;
    private Long quizId;
    private String quizTitle;
    private Timestamp startTime;
    private Timestamp endTime;
    private Integer totalScore;
    private String resultType;
    private Integer status; // 0-进行中, 1-已完成, 2-已放弃
    private Timestamp createdAt;
    private Timestamp updatedAt;

    // 构造函数
    public UserQuizRecord() {}

    public UserQuizRecord(Long userId, Long quizId) {
        this.userId = userId;
        this.quizId = quizId;
        this.status = 0; // 进行中
        this.totalScore = 0;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getQuizId() {
        return quizId;
    }

    public void setQuizId(Long quizId) {
        this.quizId = quizId;
    }

    public String getQuizTitle() {
        return quizTitle;
    }

    public void setQuizTitle(String quizTitle) {
        this.quizTitle = quizTitle;
    }

    public Timestamp getStartTime() {
        return startTime;
    }

    public void setStartTime(Timestamp startTime) {
        this.startTime = startTime;
    }

    public Timestamp getEndTime() {
        return endTime;
    }

    public void setEndTime(Timestamp endTime) {
        this.endTime = endTime;
    }

    public Integer getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(Integer totalScore) {
        this.totalScore = totalScore;
    }

    public String getResultType() {
        return resultType;
    }

    public void setResultType(String resultType) {
        this.resultType = resultType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Timestamp getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Timestamp createdAt) {
        this.createdAt = createdAt;
    }

    public Timestamp getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Timestamp updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "UserQuizRecord{" +
                "id=" + id +
                ", userId=" + userId +
                ", quizId=" + quizId +
                ", totalScore=" + totalScore +
                ", status=" + status +
                '}';
    }
}
