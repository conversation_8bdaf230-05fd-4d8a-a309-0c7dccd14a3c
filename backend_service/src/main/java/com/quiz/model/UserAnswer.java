package com.quiz.model;

import java.sql.Timestamp;

/**
 * 用户答案实体类
 */
public class UserAnswer {
    private Long id;
    private Long recordId;
    private Long questionId;
    private Long optionId;
    private String answerText;
    private Integer score;
    private Timestamp createdAt;

    // 构造函数
    public UserAnswer() {}

    public UserAnswer(Long recordId, Long questionId, Long optionId, Integer score) {
        this.recordId = recordId;
        this.questionId = questionId;
        this.optionId = optionId;
        this.score = score;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRecordId() {
        return recordId;
    }

    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }

    public Long getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }

    public Long getOptionId() {
        return optionId;
    }

    public void setOptionId(Long optionId) {
        this.optionId = optionId;
    }

    public String getAnswerText() {
        return answerText;
    }

    public void setAnswerText(String answerText) {
        this.answerText = answerText;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public Timestamp getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Timestamp createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public String toString() {
        return "UserAnswer{" +
                "id=" + id +
                ", recordId=" + recordId +
                ", questionId=" + questionId +
                ", optionId=" + optionId +
                ", score=" + score +
                '}';
    }
}
