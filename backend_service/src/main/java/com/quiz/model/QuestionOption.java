package com.quiz.model;

import java.sql.Timestamp;

/**
 * 题目选项实体类
 */
public class QuestionOption {
    private Long id;
    private Long questionId;
    private String optionText;
    private String optionValue;
    private Integer score;
    private Integer sortOrder;
    private Timestamp createdAt;

    // 构造函数
    public QuestionOption() {}

    public QuestionOption(Long questionId, String optionText, Integer score) {
        this.questionId = questionId;
        this.optionText = optionText;
        this.score = score;
        this.sortOrder = 0;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }

    public String getOptionText() {
        return optionText;
    }

    public void setOptionText(String optionText) {
        this.optionText = optionText;
    }

    public String getOptionValue() {
        return optionValue;
    }

    public void setOptionValue(String optionValue) {
        this.optionValue = optionValue;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Timestamp getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Timestamp createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public String toString() {
        return "QuestionOption{" +
                "id=" + id +
                ", questionId=" + questionId +
                ", optionText='" + optionText + '\'' +
                ", score=" + score +
                '}';
    }
}
