package com.quiz.servlet;

import com.quiz.dao.QuizDao;
import com.quiz.dao.UserQuizRecordDao;
import com.quiz.model.*;
import com.quiz.util.JsonUtil;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 答题相关Servlet
 * 处理答题提交、结果计算等请求
 */
public class AnswerServlet extends HttpServlet {
    private QuizDao quizDao;
    private UserQuizRecordDao recordDao;
    
    @Override
    public void init() throws ServletException {
        super.init();
        quizDao = new QuizDao();
        recordDao = new UserQuizRecordDao();
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        if (pathInfo == null) {
            JsonUtil.sendBadRequest(response, "缺少请求路径");
            return;
        }
        
        try {
            if (pathInfo.equals("/start")) {
                handleStartQuiz(request, response);
            } else if (pathInfo.equals("/submit")) {
                handleSubmitAnswers(request, response);
            } else {
                JsonUtil.sendNotFound(response, "未找到请求的接口");
            }
        } catch (Exception e) {
            e.printStackTrace();
            JsonUtil.sendError(response, "服务器内部错误: " + e.getMessage());
        }
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        if (pathInfo == null) {
            JsonUtil.sendBadRequest(response, "缺少请求路径");
            return;
        }
        
        try {
            if (pathInfo.matches("/result/\\d+")) {
                // 获取测评结果 /api/answer/result/123
                String[] parts = pathInfo.split("/");
                Long recordId = Long.parseLong(parts[2]);
                handleGetResult(request, response, recordId);
            } else {
                JsonUtil.sendNotFound(response, "未找到请求的接口");
            }
        } catch (Exception e) {
            e.printStackTrace();
            JsonUtil.sendError(response, "服务器内部错误: " + e.getMessage());
        }
    }
    
    /**
     * 开始测评
     */
    private void handleStartQuiz(HttpServletRequest request, HttpServletResponse response)
            throws IOException, SQLException {
        
        // 获取用户ID
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            JsonUtil.sendUnauthorized(response, "用户未登录");
            return;
        }
        
        // 读取请求体
        String requestBody = readRequestBody(request);
        if (requestBody == null || requestBody.trim().isEmpty()) {
            JsonUtil.sendBadRequest(response, "请求体不能为空");
            return;
        }
        
        try {
            Map<String, Object> requestData = JsonUtil.fromJson(requestBody, Map.class);
            Object quizIdObj = requestData.get("quizId");
            
            if (quizIdObj == null) {
                JsonUtil.sendBadRequest(response, "测评ID不能为空");
                return;
            }
            
            Long quizId = Long.parseLong(quizIdObj.toString());
            
            // 检查测评是否存在
            Quiz quiz = quizDao.getQuizById(quizId);
            if (quiz == null) {
                JsonUtil.sendNotFound(response, "测评不存在");
                return;
            }
            
            // 创建测评记录
            UserQuizRecord record = new UserQuizRecord(userId, quizId);
            Long recordId = recordDao.createRecord(record);
            
            // 增加测评参与次数
            quizDao.incrementTakeCount(quizId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("recordId", recordId);
            result.put("quizId", quizId);
            result.put("quizTitle", quiz.getTitle());
            result.put("questionCount", quiz.getQuestionCount());
            
            JsonUtil.sendSuccess(response, result);
            
        } catch (Exception e) {
            e.printStackTrace();
            JsonUtil.sendError(response, "开始测评失败: " + e.getMessage());
        }
    }
    
    /**
     * 提交答案
     */
    private void handleSubmitAnswers(HttpServletRequest request, HttpServletResponse response)
            throws IOException, SQLException {
        
        // 获取用户ID
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            JsonUtil.sendUnauthorized(response, "用户未登录");
            return;
        }
        
        // 读取请求体
        String requestBody = readRequestBody(request);
        if (requestBody == null || requestBody.trim().isEmpty()) {
            JsonUtil.sendBadRequest(response, "请求体不能为空");
            return;
        }
        
        try {
            Map<String, Object> requestData = JsonUtil.fromJson(requestBody, Map.class);
            Object recordIdObj = requestData.get("recordId");
            Object answersObj = requestData.get("answers");
            
            if (recordIdObj == null) {
                JsonUtil.sendBadRequest(response, "记录ID不能为空");
                return;
            }
            
            if (answersObj == null) {
                JsonUtil.sendBadRequest(response, "答案不能为空");
                return;
            }
            
            Long recordId = Long.parseLong(recordIdObj.toString());
            
            // 检查记录是否存在且属于当前用户
            UserQuizRecord record = recordDao.getRecordById(recordId);
            if (record == null || !record.getUserId().equals(userId)) {
                JsonUtil.sendNotFound(response, "测评记录不存在");
                return;
            }
            
            if (record.getStatus() != 0) {
                JsonUtil.sendBadRequest(response, "测评已完成，不能重复提交");
                return;
            }
            
            // 处理答案
            Map<String, Object> answers = (Map<String, Object>) answersObj;
            int totalScore = 0;
            
            for (Map.Entry<String, Object> entry : answers.entrySet()) {
                Long questionId = Long.parseLong(entry.getKey());
                Object answerValue = entry.getValue();
                
                // 根据答案类型处理
                if (answerValue instanceof Number) {
                    // 单选题 - 选项ID
                    Long optionId = Long.parseLong(answerValue.toString());
                    
                    // 获取选项分数
                    List<QuestionOption> options = quizDao.getQuestionOptions(questionId);
                    int score = 0;
                    for (QuestionOption option : options) {
                        if (option.getId().equals(optionId)) {
                            score = option.getScore();
                            break;
                        }
                    }
                    
                    // 保存答案
                    UserAnswer answer = new UserAnswer(recordId, questionId, optionId, score);
                    recordDao.saveAnswer(answer);
                    totalScore += score;
                    
                } else if (answerValue instanceof List) {
                    // 多选题 - 选项ID列表
                    List<Object> optionIds = (List<Object>) answerValue;
                    List<QuestionOption> options = quizDao.getQuestionOptions(questionId);
                    
                    for (Object optionIdObj : optionIds) {
                        Long optionId = Long.parseLong(optionIdObj.toString());
                        int score = 0;
                        for (QuestionOption option : options) {
                            if (option.getId().equals(optionId)) {
                                score = option.getScore();
                                break;
                            }
                        }
                        
                        UserAnswer answer = new UserAnswer(recordId, questionId, optionId, score);
                        recordDao.saveAnswer(answer);
                        totalScore += score;
                    }
                } else {
                    // 文本答案或滑动打分
                    String answerText = answerValue.toString();
                    UserAnswer answer = new UserAnswer();
                    answer.setRecordId(recordId);
                    answer.setQuestionId(questionId);
                    answer.setAnswerText(answerText);
                    answer.setScore(0); // 文本答案暂时不计分
                    recordDao.saveAnswer(answer);
                }
            }
            
            // 更新测评记录
            record.setEndTime(new Timestamp(System.currentTimeMillis()));
            record.setTotalScore(totalScore);
            record.setStatus(1); // 已完成
            record.setResultType(calculateResultType(totalScore)); // 简单的结果类型计算
            recordDao.updateRecord(record);
            
            Map<String, Object> result = new HashMap<>();
            result.put("recordId", recordId);
            result.put("totalScore", totalScore);
            result.put("resultType", record.getResultType());
            result.put("status", "completed");
            
            JsonUtil.sendSuccess(response, result);
            
        } catch (Exception e) {
            e.printStackTrace();
            JsonUtil.sendError(response, "提交答案失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取测评结果
     */
    private void handleGetResult(HttpServletRequest request, HttpServletResponse response, Long recordId)
            throws IOException, SQLException {
        
        // 获取用户ID
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            JsonUtil.sendUnauthorized(response, "用户未登录");
            return;
        }
        
        // 获取测评记录
        UserQuizRecord record = recordDao.getRecordById(recordId);
        if (record == null || !record.getUserId().equals(userId)) {
            JsonUtil.sendNotFound(response, "测评记录不存在");
            return;
        }
        
        if (record.getStatus() != 1) {
            JsonUtil.sendBadRequest(response, "测评未完成");
            return;
        }
        
        // 获取答案详情
        List<UserAnswer> answers = recordDao.getRecordAnswers(recordId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("record", record);
        result.put("answers", answers);
        result.put("analysis", generateAnalysis(record)); // 生成分析报告
        
        JsonUtil.sendSuccess(response, result);
    }
    
    /**
     * 简单的结果类型计算
     */
    private String calculateResultType(int totalScore) {
        if (totalScore >= 80) {
            return "EXCELLENT";
        } else if (totalScore >= 60) {
            return "GOOD";
        } else if (totalScore >= 40) {
            return "AVERAGE";
        } else {
            return "BELOW_AVERAGE";
        }
    }
    
    /**
     * 生成分析报告
     */
    private Map<String, Object> generateAnalysis(UserQuizRecord record) {
        Map<String, Object> analysis = new HashMap<>();
        
        String resultType = record.getResultType();
        int totalScore = record.getTotalScore();
        
        switch (resultType) {
            case "EXCELLENT":
                analysis.put("title", "优秀");
                analysis.put("description", "您在此次测评中表现优秀，各方面能力均衡发展。");
                analysis.put("suggestions", "继续保持当前的良好状态，可以尝试挑战更高难度的内容。");
                break;
            case "GOOD":
                analysis.put("title", "良好");
                analysis.put("description", "您在此次测评中表现良好，大部分方面都达到了预期水平。");
                analysis.put("suggestions", "在某些方面还有提升空间，建议针对性地加强练习。");
                break;
            case "AVERAGE":
                analysis.put("title", "一般");
                analysis.put("description", "您在此次测评中表现一般，还有较大的提升空间。");
                analysis.put("suggestions", "建议系统性地学习相关知识，多加练习以提高能力水平。");
                break;
            default:
                analysis.put("title", "待提高");
                analysis.put("description", "您在此次测评中的表现有待提高，需要更多的学习和练习。");
                analysis.put("suggestions", "建议从基础开始，循序渐进地提升相关能力。");
                break;
        }
        
        analysis.put("score", totalScore);
        analysis.put("level", resultType);
        
        return analysis;
    }
    
    /**
     * 读取请求体内容
     */
    private String readRequestBody(HttpServletRequest request) throws IOException {
        StringBuilder sb = new StringBuilder();
        BufferedReader reader = request.getReader();
        String line;
        while ((line = reader.readLine()) != null) {
            sb.append(line);
        }
        return sb.toString();
    }
}
