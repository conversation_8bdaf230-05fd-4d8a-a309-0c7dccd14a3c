package com.quiz.servlet;

import com.quiz.dao.UserDao;
import com.quiz.model.User;
import com.quiz.util.*;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户认证Servlet
 * 处理用户注册、登录、token验证等认证相关请求
 */
public class AuthServlet extends HttpServlet {
    private UserDao userDao;
    
    @Override
    public void init() throws ServletException {
        super.init();
        userDao = new UserDao();
        
        // 初始化数据库连接池
        try {
            DatabaseUtil.getInstance().initialize(getServletContext());
        } catch (Exception e) {
            throw new ServletException("数据库初始化失败", e);
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        if (pathInfo == null) {
            JsonUtil.sendBadRequest(response, "缺少请求路径");
            return;
        }
        
        try {
            switch (pathInfo) {
                case "/register":
                    handleRegister(request, response);
                    break;
                case "/login":
                    handleLogin(request, response);
                    break;
                case "/refresh":
                    handleRefreshToken(request, response);
                    break;
                default:
                    JsonUtil.sendNotFound(response, "未找到请求的接口");
            }
        } catch (Exception e) {
            e.printStackTrace();
            JsonUtil.sendError(response, "服务器内部错误: " + e.getMessage());
        }
    }
    
    /**
     * 处理用户注册
     */
    private void handleRegister(HttpServletRequest request, HttpServletResponse response)
            throws IOException, SQLException {
        
        // 读取请求体
        String requestBody = readRequestBody(request);
        if (requestBody == null || requestBody.trim().isEmpty()) {
            JsonUtil.sendBadRequest(response, "请求体不能为空");
            return;
        }
        
        try {
            // 解析JSON
            Map<String, Object> requestData = JsonUtil.fromJson(requestBody, Map.class);
            String username = (String) requestData.get("username");
            String email = (String) requestData.get("email");
            String password = (String) requestData.get("password");
            String nickname = (String) requestData.get("nickname");
            
            // 验证必填字段
            if (username == null || username.trim().isEmpty()) {
                JsonUtil.sendBadRequest(response, "用户名不能为空");
                return;
            }
            if (email == null || email.trim().isEmpty()) {
                JsonUtil.sendBadRequest(response, "邮箱不能为空");
                return;
            }
            if (password == null || password.trim().isEmpty()) {
                JsonUtil.sendBadRequest(response, "密码不能为空");
                return;
            }
            
            // 验证用户名和邮箱是否已存在
            if (userDao.existsByUsername(username)) {
                JsonUtil.sendBadRequest(response, "用户名已存在");
                return;
            }
            if (userDao.existsByEmail(email)) {
                JsonUtil.sendBadRequest(response, "邮箱已存在");
                return;
            }
            
            // 创建用户
            User user = new User();
            user.setUsername(username.trim());
            user.setEmail(email.trim());
            user.setPasswordHash(PasswordUtil.hashPassword(password));
            user.setNickname(nickname != null ? nickname.trim() : username.trim());
            user.setStatus(1);
            user.setIsVip(false);
            
            Long userId = userDao.createUser(user);
            
            // 生成token
            String token = JwtUtil.generateToken(userId, username);
            
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("userId", userId);
            result.put("username", username);
            result.put("nickname", user.getNickname());
            result.put("token", token);
            
            JsonUtil.sendSuccess(response, result);
            
        } catch (Exception e) {
            e.printStackTrace();
            JsonUtil.sendError(response, "注册失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理用户登录
     */
    private void handleLogin(HttpServletRequest request, HttpServletResponse response)
            throws IOException, SQLException {
        
        // 读取请求体
        String requestBody = readRequestBody(request);
        if (requestBody == null || requestBody.trim().isEmpty()) {
            JsonUtil.sendBadRequest(response, "请求体不能为空");
            return;
        }
        
        try {
            // 解析JSON
            Map<String, Object> requestData = JsonUtil.fromJson(requestBody, Map.class);
            String username = (String) requestData.get("username");
            String password = (String) requestData.get("password");
            
            // 验证必填字段
            if (username == null || username.trim().isEmpty()) {
                JsonUtil.sendBadRequest(response, "用户名不能为空");
                return;
            }
            if (password == null || password.trim().isEmpty()) {
                JsonUtil.sendBadRequest(response, "密码不能为空");
                return;
            }
            
            // 查找用户（支持用户名或邮箱登录）
            User user = userDao.findByUsername(username);
            if (user == null) {
                user = userDao.findByEmail(username);
            }
            
            if (user == null) {
                JsonUtil.sendBadRequest(response, "用户不存在");
                return;
            }
            
            // 验证密码
            if (!PasswordUtil.verifyPassword(password, user.getPasswordHash())) {
                JsonUtil.sendBadRequest(response, "密码错误");
                return;
            }
            
            // 生成token
            String token = JwtUtil.generateToken(user.getId(), user.getUsername());
            
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("userId", user.getId());
            result.put("username", user.getUsername());
            result.put("nickname", user.getNickname());
            result.put("email", user.getEmail());
            result.put("isVip", user.getIsVip());
            result.put("token", token);
            
            JsonUtil.sendSuccess(response, result);
            
        } catch (Exception e) {
            e.printStackTrace();
            JsonUtil.sendError(response, "登录失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理token刷新
     */
    private void handleRefreshToken(HttpServletRequest request, HttpServletResponse response)
            throws IOException {
        
        String authHeader = request.getHeader("Authorization");
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            JsonUtil.sendUnauthorized(response, "缺少认证token");
            return;
        }
        
        String token = authHeader.substring(7);
        String newToken = JwtUtil.refreshToken(token);
        
        if (newToken == null) {
            JsonUtil.sendUnauthorized(response, "token刷新失败");
            return;
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("token", newToken);
        
        JsonUtil.sendSuccess(response, result);
    }
    
    /**
     * 读取请求体内容
     */
    private String readRequestBody(HttpServletRequest request) throws IOException {
        StringBuilder sb = new StringBuilder();
        BufferedReader reader = request.getReader();
        String line;
        while ((line = reader.readLine()) != null) {
            sb.append(line);
        }
        return sb.toString();
    }
}
