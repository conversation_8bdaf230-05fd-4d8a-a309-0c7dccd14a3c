package com.quiz.servlet;

import com.quiz.dao.UserDao;
import com.quiz.dao.UserQuizRecordDao;
import com.quiz.model.User;
import com.quiz.model.UserQuizRecord;
import com.quiz.util.JsonUtil;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户管理Servlet
 * 处理用户信息相关的请求
 */
public class UserServlet extends HttpServlet {
    private UserDao userDao;
    private UserQuizRecordDao recordDao;
    
    @Override
    public void init() throws ServletException {
        super.init();
        userDao = new UserDao();
        recordDao = new UserQuizRecordDao();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        if (pathInfo == null) {
            JsonUtil.sendBadRequest(response, "缺少请求路径");
            return;
        }
        
        try {
            if (pathInfo.equals("/profile")) {
                handleGetProfile(request, response);
            } else if (pathInfo.equals("/records")) {
                handleGetUserRecords(request, response);
            } else {
                JsonUtil.sendNotFound(response, "未找到请求的接口");
            }
        } catch (Exception e) {
            e.printStackTrace();
            JsonUtil.sendError(response, "服务器内部错误: " + e.getMessage());
        }
    }
    
    @Override
    protected void doPut(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        if (pathInfo == null) {
            JsonUtil.sendBadRequest(response, "缺少请求路径");
            return;
        }
        
        try {
            if (pathInfo.equals("/profile")) {
                handleUpdateProfile(request, response);
            } else {
                JsonUtil.sendNotFound(response, "未找到请求的接口");
            }
        } catch (Exception e) {
            e.printStackTrace();
            JsonUtil.sendError(response, "服务器内部错误: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户信息
     */
    private void handleGetProfile(HttpServletRequest request, HttpServletResponse response)
            throws IOException, SQLException {
        
        // 获取用户ID
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            JsonUtil.sendUnauthorized(response, "用户未登录");
            return;
        }
        
        User user = userDao.findById(userId);
        if (user == null) {
            JsonUtil.sendNotFound(response, "用户不存在");
            return;
        }
        
        // 构建返回数据（不包含密码）
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", user.getId());
        userInfo.put("username", user.getUsername());
        userInfo.put("email", user.getEmail());
        userInfo.put("phone", user.getPhone());
        userInfo.put("nickname", user.getNickname());
        userInfo.put("avatarUrl", user.getAvatarUrl());
        userInfo.put("gender", user.getGender());
        userInfo.put("birthDate", user.getBirthDate());
        userInfo.put("isVip", user.getIsVip());
        userInfo.put("vipExpireTime", user.getVipExpireTime());
        userInfo.put("createdAt", user.getCreatedAt());
        
        JsonUtil.sendSuccess(response, userInfo);
    }
    
    /**
     * 更新用户信息
     */
    private void handleUpdateProfile(HttpServletRequest request, HttpServletResponse response)
            throws IOException, SQLException {
        
        // 获取用户ID
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            JsonUtil.sendUnauthorized(response, "用户未登录");
            return;
        }
        
        // 读取请求体
        String requestBody = readRequestBody(request);
        if (requestBody == null || requestBody.trim().isEmpty()) {
            JsonUtil.sendBadRequest(response, "请求体不能为空");
            return;
        }
        
        try {
            Map<String, Object> requestData = JsonUtil.fromJson(requestBody, Map.class);
            
            // 获取当前用户信息
            User user = userDao.findById(userId);
            if (user == null) {
                JsonUtil.sendNotFound(response, "用户不存在");
                return;
            }
            
            // 更新允许修改的字段
            if (requestData.containsKey("nickname")) {
                user.setNickname((String) requestData.get("nickname"));
            }
            
            if (requestData.containsKey("avatarUrl")) {
                user.setAvatarUrl((String) requestData.get("avatarUrl"));
            }
            
            if (requestData.containsKey("gender")) {
                Object genderObj = requestData.get("gender");
                if (genderObj != null) {
                    user.setGender(Integer.parseInt(genderObj.toString()));
                }
            }
            
            if (requestData.containsKey("phone")) {
                user.setPhone((String) requestData.get("phone"));
            }
            
            if (requestData.containsKey("birthDate")) {
                String birthDateStr = (String) requestData.get("birthDate");
                if (birthDateStr != null && !birthDateStr.trim().isEmpty()) {
                    try {
                        java.sql.Date birthDate = java.sql.Date.valueOf(birthDateStr);
                        user.setBirthDate(birthDate);
                    } catch (IllegalArgumentException e) {
                        JsonUtil.sendBadRequest(response, "出生日期格式错误，请使用YYYY-MM-DD格式");
                        return;
                    }
                }
            }
            
            // 更新用户信息
            boolean success = userDao.updateUser(user);
            if (success) {
                // 构建返回数据
                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("id", user.getId());
                userInfo.put("username", user.getUsername());
                userInfo.put("email", user.getEmail());
                userInfo.put("phone", user.getPhone());
                userInfo.put("nickname", user.getNickname());
                userInfo.put("avatarUrl", user.getAvatarUrl());
                userInfo.put("gender", user.getGender());
                userInfo.put("birthDate", user.getBirthDate());
                userInfo.put("isVip", user.getIsVip());
                userInfo.put("vipExpireTime", user.getVipExpireTime());
                
                JsonUtil.sendSuccess(response, userInfo);
            } else {
                JsonUtil.sendError(response, "更新用户信息失败");
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            JsonUtil.sendError(response, "更新用户信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户测评记录
     */
    private void handleGetUserRecords(HttpServletRequest request, HttpServletResponse response)
            throws IOException, SQLException {
        
        // 获取用户ID
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            JsonUtil.sendUnauthorized(response, "用户未登录");
            return;
        }
        
        // 获取分页参数
        String pageParam = request.getParameter("page");
        String pageSizeParam = request.getParameter("pageSize");
        
        int page = 1;
        if (pageParam != null && !pageParam.trim().isEmpty()) {
            try {
                page = Integer.parseInt(pageParam);
                if (page < 1) page = 1;
            } catch (NumberFormatException e) {
                // 使用默认值
            }
        }
        
        int pageSize = 20;
        if (pageSizeParam != null && !pageSizeParam.trim().isEmpty()) {
            try {
                pageSize = Integer.parseInt(pageSizeParam);
                if (pageSize < 1) pageSize = 20;
                if (pageSize > 100) pageSize = 100;
            } catch (NumberFormatException e) {
                // 使用默认值
            }
        }
        
        int offset = (page - 1) * pageSize;
        
        List<UserQuizRecord> records = recordDao.getUserRecords(userId, offset, pageSize);
        
        Map<String, Object> result = new HashMap<>();
        result.put("list", records);
        result.put("page", page);
        result.put("pageSize", pageSize);
        result.put("total", records.size()); // 简化实现
        
        JsonUtil.sendSuccess(response, result);
    }
    
    /**
     * 读取请求体内容
     */
    private String readRequestBody(HttpServletRequest request) throws IOException {
        StringBuilder sb = new StringBuilder();
        BufferedReader reader = request.getReader();
        String line;
        while ((line = reader.readLine()) != null) {
            sb.append(line);
        }
        return sb.toString();
    }
}
