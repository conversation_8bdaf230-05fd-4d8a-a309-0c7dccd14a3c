package com.quiz.filter;

import com.quiz.util.JsonUtil;
import com.quiz.util.JwtUtil;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 用户认证过滤器
 * 验证JWT Token
 */
public class AuthFilter implements Filter {
    
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // 初始化过滤器
    }
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        // 获取Authorization头
        String authHeader = httpRequest.getHeader("Authorization");
        
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            JsonUtil.sendUnauthorized(httpResponse, "缺少认证token");
            return;
        }
        
        // 提取token
        String token = authHeader.substring(7);
        
        try {
            // 验证token
            Long userId = JwtUtil.validateToken(token);
            if (userId == null) {
                JsonUtil.sendUnauthorized(httpResponse, "无效的token");
                return;
            }
            
            // 将用户ID设置到请求属性中
            httpRequest.setAttribute("userId", userId);
            
            // 继续处理请求
            chain.doFilter(request, response);
            
        } catch (Exception e) {
            JsonUtil.sendUnauthorized(httpResponse, "token验证失败: " + e.getMessage());
        }
    }
    
    @Override
    public void destroy() {
        // 销毁过滤器
    }
}
