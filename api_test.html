<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QUIZ API 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #E6D4EF 0%, #D1E5E9 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .api-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #E6D4EF;
            border-radius: 12px;
            background: #fafafa;
        }
        
        .api-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .api-url {
            background: #2c3e50;
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            margin: 10px 0;
            word-break: break-all;
        }
        
        .test-button {
            background: linear-gradient(135deg, #E6D4EF 0%, #D1E5E9 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(230, 212, 239, 0.4);
        }
        
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online {
            background-color: #28a745;
        }
        
        .status-offline {
            background-color: #dc3545;
        }
        
        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧠 QUIZ 心理测评系统 API 测试</h1>
        
        <div class="info-box">
            <h3>📋 服务状态</h3>
            <p><span id="status-indicator" class="status-indicator status-offline"></span>
               <span id="status-text">检查中...</span></p>
            <p><strong>服务地址:</strong> http://localhost:8080/quiz-api</p>
            <p><strong>API基础路径:</strong> http://localhost:8080/quiz-api/api</p>
        </div>
        
        <div class="api-section">
            <div class="api-title">1. 📝 获取测评列表</div>
            <div class="api-url">GET http://localhost:8080/quiz-api/api/quiz/list</div>
            <button class="test-button" onclick="testAPI('/quiz/list', 'GET')">测试接口</button>
            <div id="result-quiz-list" class="result" style="display: none;"></div>
        </div>
        
        <div class="api-section">
            <div class="api-title">2. 🔍 获取测评详情</div>
            <div class="api-url">GET http://localhost:8080/quiz-api/api/quiz/1</div>
            <button class="test-button" onclick="testAPI('/quiz/1', 'GET')">测试接口</button>
            <div id="result-quiz-detail" class="result" style="display: none;"></div>
        </div>
        
        <div class="api-section">
            <div class="api-title">3. ❓ 获取测评题目</div>
            <div class="api-url">GET http://localhost:8080/quiz-api/api/quiz/1/questions</div>
            <button class="test-button" onclick="testAPI('/quiz/1/questions', 'GET')">测试接口</button>
            <div id="result-quiz-questions" class="result" style="display: none;"></div>
        </div>
        
        <div class="api-section">
            <div class="api-title">4. 👤 用户注册</div>
            <div class="api-url">POST http://localhost:8080/quiz-api/api/auth/register</div>
            <button class="test-button" onclick="testUserRegister()">测试注册</button>
            <div id="result-register" class="result" style="display: none;"></div>
        </div>
        
        <div class="api-section">
            <div class="api-title">5. 🔐 用户登录</div>
            <div class="api-url">POST http://localhost:8080/quiz-api/api/auth/login</div>
            <button class="test-button" onclick="testUserLogin()">测试登录</button>
            <div id="result-login" class="result" style="display: none;"></div>
        </div>
        
        <div class="info-box">
            <h3>💡 使用说明</h3>
            <ul>
                <li>确保后端服务已启动: <code>cd backend_service && mvn tomcat7:run</code></li>
                <li>确保MySQL数据库已配置并运行</li>
                <li>点击各个"测试接口"按钮查看API响应</li>
                <li>如果看到404错误，请检查服务是否正常启动</li>
                <li>如果看到CORS错误，这是正常的（浏览器安全限制）</li>
            </ul>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/quiz-api/api';
        
        // 检查服务状态
        async function checkServiceStatus() {
            try {
                const response = await fetch(API_BASE + '/quiz/list');
                if (response.ok) {
                    document.getElementById('status-indicator').className = 'status-indicator status-online';
                    document.getElementById('status-text').textContent = '服务正常运行';
                } else {
                    throw new Error('Service not available');
                }
            } catch (error) {
                document.getElementById('status-indicator').className = 'status-indicator status-offline';
                document.getElementById('status-text').textContent = '服务离线或CORS限制';
            }
        }
        
        // 测试API
        async function testAPI(endpoint, method = 'GET', data = null) {
            const resultId = 'result-' + endpoint.replace(/[\/\d]/g, '').replace(/^-/, '');
            const resultElement = document.getElementById(resultId) || 
                                document.getElementById('result-' + endpoint.split('/')[1]);
            
            if (!resultElement) {
                console.error('Result element not found for:', endpoint);
                return;
            }
            
            resultElement.style.display = 'block';
            resultElement.textContent = '请求中...';
            resultElement.className = 'result';
            
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(API_BASE + endpoint, options);
                const result = await response.json();
                
                resultElement.textContent = JSON.stringify(result, null, 2);
                resultElement.className = response.ok ? 'result success' : 'result error';
                
            } catch (error) {
                resultElement.textContent = `错误: ${error.message}\n\n这通常是由于CORS限制导致的。\n请直接在浏览器中访问: ${API_BASE + endpoint}`;
                resultElement.className = 'result error';
            }
        }
        
        // 测试用户注册
        function testUserRegister() {
            const userData = {
                username: 'testuser_' + Date.now(),
                email: 'test_' + Date.now() + '@example.com',
                password: '123456'
            };
            testAPI('/auth/register', 'POST', userData);
        }
        
        // 测试用户登录
        function testUserLogin() {
            const loginData = {
                username: 'testuser',
                password: '123456'
            };
            testAPI('/auth/login', 'POST', loginData);
        }
        
        // 页面加载时检查服务状态
        window.onload = function() {
            checkServiceStatus();
        };
    </script>
</body>
</html>
