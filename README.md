# QUIZ 心理测评应用系统

## 项目概述
一个完整的心理测评应用系统，包含Flutter移动端、Java Servlet后台服务和React管理后台。

## 系统架构

### 技术栈
- **移动端**: Flutter (iOS优先)
- **后台服务**: Java Servlet + MySQL
- **管理后台**: React + Ant Design
- **数据库**: MySQL 8.0

### 项目结构
```
quiz/
├── flutter_app/          # Flutter移动应用
├── backend_service/      # Java Servlet后台服务
├── admin_panel/          # React管理后台
├── database/            # 数据库脚本
└── docs/               # 项目文档
```

## 核心功能模块

### 1. 移动端 (Flutter)
- 🏠 首页：测评推荐、历史记录、Banner
- 📝 测评模块：测评列表、答题界面、结果展示
- 📊 报告中心：历史记录、详细报告、分享功能
- 👤 用户中心：登录注册、个人设置、会员功能

### 2. 后台服务 (Java Servlet)
- 用户认证与管理
- 测评题库管理
- 答题逻辑处理
- 结果计算与报告生成
- 数据统计分析

### 3. 管理后台 (React)
- 测评内容管理
- 题库编辑器
- 用户数据管理
- 系统配置
- 数据分析面板

## 数据库设计

### 核心数据表
- users: 用户信息
- quizzes: 测评基本信息
- questions: 题目信息
- question_options: 题目选项
- user_quiz_records: 用户答题记录
- quiz_results: 测评结果模板
- user_answers: 用户答案详情

## 开发环境要求
- Flutter SDK
- Java 8+
- Node.js 16+
- MySQL 8.0
- IDE: VS Code / IntelliJ IDEA

## 快速开始
详细的开发和部署说明请参考各子项目的README文件。
