# QUIZ 心理测评系统 - 部署指南

## 🚀 系统架构概览

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Flutter App   │    │  React Admin    │    │  Java Backend   │
│   (移动端)       │    │   (管理后台)     │    │   (API服务)      │
│   Port: N/A     │    │   Port: 3000    │    │   Port: 8080    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   MySQL DB      │
                    │   Port: 3306    │
                    └─────────────────┘
```

## 📋 部署前准备

### 系统要求
- **操作系统**: Linux/macOS/Windows
- **Java**: JDK 8+
- **Node.js**: 16+
- **Flutter**: 3.x
- **MySQL**: 8.0+
- **Maven**: 3.6+

### 必需软件安装
```bash
# Java (Ubuntu/Debian)
sudo apt update
sudo apt install openjdk-8-jdk maven

# Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Flutter
# 下载Flutter SDK并配置环境变量
export PATH="$PATH:/path/to/flutter/bin"

# MySQL
sudo apt install mysql-server
```

## 🗄️ 数据库部署

### 1. 创建数据库
```bash
# 登录MySQL
mysql -u root -p

# 创建数据库
CREATE DATABASE quiz CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 创建用户（可选）
CREATE USER 'quiz_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON quiz.* TO 'quiz_user'@'localhost';
FLUSH PRIVILEGES;
```

### 2. 初始化数据表
```bash
# 执行初始化脚本
mysql -u root -p quiz < database/init.sql
```

### 3. 验证数据库
```sql
USE quiz;
SHOW TABLES;
SELECT COUNT(*) FROM users;
SELECT COUNT(*) FROM quizzes;
```

## 🔧 后台服务部署

### 1. 编译项目
```bash
cd backend_service
mvn clean compile
mvn clean package
```

### 2. 配置数据库连接
编辑 `src/main/java/com/quiz/util/DatabaseUtil.java`：
```java
private static final String DB_URL = "********************************";
private static final String DB_USER = "root";
private static final String DB_PASSWORD = "your_password";
```

### 3. 启动服务
```bash
# 开发环境
mvn tomcat7:run

# 生产环境 (使用外部Tomcat)
cp target/quiz-api.war /path/to/tomcat/webapps/
```

### 4. 验证服务
```bash
# 测试API连通性
curl http://localhost:8080/quiz-api/api/quiz/list

# 测试用户注册
curl -X POST http://localhost:8080/quiz-api/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"test","email":"<EMAIL>","password":"123456"}'
```

## 💻 React管理后台部署

### 1. 安装依赖
```bash
cd admin_panel
npm install
```

### 2. 配置API地址
编辑 `src/services/api.js`（如果存在）：
```javascript
const API_BASE_URL = 'http://localhost:8080/quiz-api/api';
```

### 3. 构建项目
```bash
# 开发环境
npm start

# 生产环境
npm run build
```

### 4. 部署到Web服务器
```bash
# 使用Nginx
sudo cp -r build/* /var/www/html/admin/

# Nginx配置示例
server {
    listen 80;
    server_name admin.quiz.com;
    root /var/www/html/admin;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /api {
        proxy_pass http://localhost:8080/quiz-api/api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 📱 Flutter应用部署

### 1. 安装依赖
```bash
cd flutter_app
flutter pub get
```

### 2. 配置API地址
编辑 `lib/services/api_service.dart`：
```dart
static const String baseUrl = 'http://your-server.com:8080/quiz-api/api';
```

### 3. 构建应用

#### iOS构建
```bash
# 确保iOS开发环境配置正确
flutter doctor

# 构建iOS应用
flutter build ios --release

# 生成IPA文件
flutter build ipa
```

#### Android构建
```bash
# 构建Android APK
flutter build apk --release

# 构建Android App Bundle
flutter build appbundle --release
```

## 🔍 系统测试

### 1. 单元测试
```bash
# 后台服务测试
cd backend_service
mvn test

# Flutter应用测试
cd flutter_app
flutter test

# React管理后台测试
cd admin_panel
npm test
```

### 2. 集成测试
```bash
# 测试完整流程
# 1. 用户注册
curl -X POST http://localhost:8080/quiz-api/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","email":"<EMAIL>","password":"123456"}'

# 2. 用户登录
curl -X POST http://localhost:8080/quiz-api/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","password":"123456"}'

# 3. 获取测评列表
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8080/quiz-api/api/quiz/list

# 4. 获取测评详情
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8080/quiz-api/api/quiz/1
```

### 3. 性能测试
```bash
# 使用Apache Bench测试API性能
ab -n 1000 -c 10 http://localhost:8080/quiz-api/api/quiz/list

# 使用JMeter进行负载测试
# 创建测试计划，模拟用户并发访问
```

## 🔒 安全配置

### 1. 数据库安全
```sql
-- 删除默认用户
DELETE FROM mysql.user WHERE User='';
DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');

-- 设置强密码策略
SET GLOBAL validate_password.policy=STRONG;
```

### 2. 应用安全
```java
// 配置CORS
response.setHeader("Access-Control-Allow-Origin", "https://your-domain.com");
response.setHeader("Access-Control-Allow-Credentials", "true");

// 配置HTTPS
// 在生产环境中使用SSL证书
```

### 3. 网络安全
```bash
# 配置防火墙
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw allow 8080  # API服务
sudo ufw enable
```

## 📊 监控和日志

### 1. 应用监控
```bash
# 使用systemd管理服务
sudo systemctl enable tomcat
sudo systemctl start tomcat
sudo systemctl status tomcat
```

### 2. 日志配置
```xml
<!-- logback.xml -->
<configuration>
    <appender name="FILE" class="ch.qos.logback.core.FileAppender">
        <file>/var/log/quiz/application.log</file>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    <root level="INFO">
        <appender-ref ref="FILE" />
    </root>
</configuration>
```

### 3. 数据库监控
```sql
-- 监控数据库性能
SHOW PROCESSLIST;
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Queries';
```

## 🚨 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务状态
   - 验证连接参数
   - 检查防火墙设置

2. **API请求失败**
   - 检查Tomcat服务状态
   - 验证CORS配置
   - 检查日志文件

3. **Flutter应用无法连接**
   - 检查网络权限
   - 验证API地址配置
   - 检查证书配置

### 日志查看
```bash
# Tomcat日志
tail -f /path/to/tomcat/logs/catalina.out

# MySQL日志
tail -f /var/log/mysql/error.log

# 系统日志
journalctl -u tomcat -f
```

## 📈 性能优化

### 1. 数据库优化
```sql
-- 添加索引
CREATE INDEX idx_user_email ON users(email);
CREATE INDEX idx_quiz_category ON quizzes(category_id);
CREATE INDEX idx_record_user ON user_quiz_records(user_id);

-- 优化查询
EXPLAIN SELECT * FROM quizzes WHERE category_id = 1;
```

### 2. 应用优化
```java
// 连接池配置
private static final int MAX_CONNECTIONS = 20;
private static final int MIN_CONNECTIONS = 5;

// 缓存配置
@Cacheable("quizzes")
public List<Quiz> getQuizzes() {
    // 实现缓存逻辑
}
```

### 3. 前端优化
```javascript
// React代码分割
const QuizManagement = lazy(() => import('./pages/QuizManagement'));

// Flutter资源优化
flutter build apk --split-per-abi
```

---

**部署完成后，请访问以下地址验证系统：**
- 管理后台: http://localhost:3000
- API文档: http://localhost:8080/quiz-api/api
- 移动应用: 通过Flutter构建的应用包安装
