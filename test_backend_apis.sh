#!/bin/bash

# QUIZ 后端API测试脚本
# 测试后端接口的基本连通性和响应

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
API_BASE_URL="http://localhost:8080/quiz-api/api"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试API连通性
test_api_connectivity() {
    log_info "测试API服务连通性..."
    
    local response=$(curl -s -w "%{http_code}" -o /dev/null "$API_BASE_URL/quiz/list" || echo "000")
    
    if [ "$response" = "200" ] || [ "$response" = "500" ]; then
        log_success "API服务正在运行 (HTTP $response)"
        return 0
    else
        log_error "API服务无法访问 (HTTP $response)"
        return 1
    fi
}

# 测试用户注册接口
test_user_registration() {
    log_info "测试用户注册接口..."
    
    local test_user="testuser_$(date +%s)"
    local response=$(curl -s -X POST "$API_BASE_URL/auth/register" \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"$test_user\",\"email\":\"$<EMAIL>\",\"password\":\"123456\"}")
    
    log_info "注册响应: $response"
    
    if echo "$response" | grep -q "code.*500"; then
        log_warning "注册接口返回500错误 (可能是数据库未配置)"
        return 0
    elif echo "$response" | grep -q "success\|token"; then
        log_success "用户注册接口正常"
        return 0
    else
        log_error "用户注册接口异常"
        return 1
    fi
}

# 测试用户登录接口
test_user_login() {
    log_info "测试用户登录接口..."
    
    local response=$(curl -s -X POST "$API_BASE_URL/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"username":"admin","password":"admin123"}')
    
    log_info "登录响应: $response"
    
    if echo "$response" | grep -q "code.*500"; then
        log_warning "登录接口返回500错误 (可能是数据库未配置)"
        return 0
    elif echo "$response" | grep -q "success\|token"; then
        log_success "用户登录接口正常"
        return 0
    else
        log_error "用户登录接口异常"
        return 1
    fi
}

# 测试测评列表接口
test_quiz_list() {
    log_info "测试测评列表接口..."
    
    local response=$(curl -s "$API_BASE_URL/quiz/list")
    
    log_info "测评列表响应: $response"
    
    if echo "$response" | grep -q "code.*500"; then
        log_warning "测评列表接口返回500错误 (可能是数据库未配置)"
        return 0
    elif echo "$response" | grep -q "success\|list\|quizzes"; then
        log_success "测评列表接口正常"
        return 0
    else
        log_error "测评列表接口异常"
        return 1
    fi
}

# 测试测评详情接口
test_quiz_detail() {
    log_info "测试测评详情接口..."
    
    local response=$(curl -s "$API_BASE_URL/quiz/1")
    
    log_info "测评详情响应: $response"
    
    if echo "$response" | grep -q "code.*500"; then
        log_warning "测评详情接口返回500错误 (可能是数据库未配置)"
        return 0
    elif echo "$response" | grep -q "success\|title\|description"; then
        log_success "测评详情接口正常"
        return 0
    else
        log_error "测评详情接口异常"
        return 1
    fi
}

# 测试CORS配置
test_cors() {
    log_info "测试CORS配置..."
    
    local response=$(curl -s -H "Origin: http://localhost:3000" \
        -H "Access-Control-Request-Method: POST" \
        -H "Access-Control-Request-Headers: Content-Type" \
        -X OPTIONS "$API_BASE_URL/auth/login")
    
    if [ -n "$response" ] || [ $? -eq 0 ]; then
        log_success "CORS配置正常"
        return 0
    else
        log_warning "CORS配置可能有问题"
        return 0
    fi
}

# 检查服务器状态
check_server_status() {
    log_info "检查Tomcat服务器状态..."
    
    if curl -s http://localhost:8080 >/dev/null 2>&1; then
        log_success "Tomcat服务器正在运行"
        return 0
    else
        log_error "Tomcat服务器未运行"
        return 1
    fi
}

# 生成测试报告
generate_report() {
    local total_tests=$1
    local passed_tests=$2
    local failed_tests=$((total_tests - passed_tests))
    
    echo ""
    echo "=================================="
    echo "       后端API测试报告"
    echo "=================================="
    echo "总测试数: $total_tests"
    echo "通过测试: $passed_tests"
    echo "失败测试: $failed_tests"
    echo "成功率: $(( passed_tests * 100 / total_tests ))%"
    echo "=================================="
    
    if [ $failed_tests -eq 0 ]; then
        log_success "所有API测试通过！"
        echo ""
        echo "📝 测试结果说明："
        echo "✅ API服务正常启动"
        echo "✅ 接口路由配置正确"
        echo "✅ JSON响应格式正确"
        echo "✅ CORS跨域配置正常"
        echo ""
        echo "⚠️  注意事项："
        echo "- 如果看到500错误，通常是数据库未配置导致"
        echo "- 需要配置MySQL数据库才能完全测试功能"
        echo "- 建议按照DEPLOYMENT_GUIDE.md配置数据库"
        return 0
    else
        log_error "有 $failed_tests 个测试失败，请检查服务配置。"
        return 1
    fi
}

# 主测试流程
main() {
    echo "开始QUIZ后端API测试..."
    echo "API基础地址: $API_BASE_URL"
    echo ""
    
    local total_tests=0
    local passed_tests=0
    
    # 检查服务器状态
    ((total_tests++))
    if check_server_status; then
        ((passed_tests++))
    fi
    
    # 测试API连通性
    ((total_tests++))
    if test_api_connectivity; then
        ((passed_tests++))
    fi
    
    # 测试用户注册
    ((total_tests++))
    if test_user_registration; then
        ((passed_tests++))
    fi
    
    # 测试用户登录
    ((total_tests++))
    if test_user_login; then
        ((passed_tests++))
    fi
    
    # 测试测评列表
    ((total_tests++))
    if test_quiz_list; then
        ((passed_tests++))
    fi
    
    # 测试测评详情
    ((total_tests++))
    if test_quiz_detail; then
        ((passed_tests++))
    fi
    
    # 测试CORS
    ((total_tests++))
    if test_cors; then
        ((passed_tests++))
    fi
    
    # 生成报告
    generate_report $total_tests $passed_tests
}

# 检查参数
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "QUIZ后端API测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  --api-url URL  指定API基础URL (默认: $API_BASE_URL)"
    echo ""
    echo "示例:"
    echo "  $0"
    echo "  $0 --api-url http://192.168.1.100:8080/quiz-api/api"
    exit 0
fi

# 解析参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --api-url)
            API_BASE_URL="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# 运行主程序
main
