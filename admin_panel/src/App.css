/* QUIZ 管理后台样式 */
.App {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 自定义Ant Design主题色 */
:root {
  --primary-color: #E6D4EF;
  --secondary-color: #D1E5E9;
  --accent-color: #FFE4CC;
  --soft-pink: #F8E8F5;
}

/* 覆盖Ant Design的主色调 */
.ant-btn-primary {
  background: linear-gradient(135deg, #E6D4EF 0%, #D1E5E9 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(230, 212, 239, 0.3);
}

.ant-btn-primary:hover,
.ant-btn-primary:focus {
  background: linear-gradient(135deg, #d4c2e0 0%, #bdd4d9 100%);
  box-shadow: 0 4px 12px rgba(230, 212, 239, 0.4);
}

/* 菜单样式 */
.ant-menu-item-selected {
  background-color: rgba(230, 212, 239, 0.1) !important;
  border-right: 3px solid #E6D4EF !important;
}

.ant-menu-item:hover {
  background-color: rgba(230, 212, 239, 0.05) !important;
}

/* 卡片样式 */
.ant-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

/* 表格样式 */
.ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

/* 统计数字样式 */
.ant-statistic-content {
  font-weight: 600;
}

/* 进度条样式 */
.ant-progress-bg {
  background: linear-gradient(135deg, #E6D4EF 0%, #D1E5E9 100%);
}

/* 标签样式 */
.ant-tag {
  border-radius: 6px;
  font-size: 12px;
}

/* 模态框样式 */
.ant-modal-header {
  border-radius: 8px 8px 0 0;
}

.ant-modal-content {
  border-radius: 8px;
}

/* 输入框样式 */
.ant-input,
.ant-select-selector {
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-sider {
    position: fixed !important;
    height: 100vh;
    z-index: 999;
  }
  
  .ant-layout-content {
    margin-left: 0 !important;
  }
}
