import React from 'react';
import { Row, Col, Card, Statistic, Typography, Progress, List, Avatar, Tag } from 'antd';
import {
  UserOutlined,
  FileTextOutlined,
  BarChartOutlined,
  TrophyOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  // 模拟数据
  const stats = [
    {
      title: '总用户数',
      value: 1234,
      prefix: <UserOutlined style={{ color: '#E6D4EF' }} />,
      suffix: '人',
      trend: 12.5,
      trendUp: true
    },
    {
      title: '测评总数',
      value: 56,
      prefix: <FileTextOutlined style={{ color: '#D1E5E9' }} />,
      suffix: '个',
      trend: 8.2,
      trendUp: true
    },
    {
      title: '今日完成测评',
      value: 89,
      prefix: <BarChartOutlined style={{ color: '#FFE4CC' }} />,
      suffix: '次',
      trend: -2.1,
      trendUp: false
    },
    {
      title: 'VIP用户',
      value: 156,
      prefix: <TrophyOutlined style={{ color: '#F8E8F5' }} />,
      suffix: '人',
      trend: 15.3,
      trendUp: true
    }
  ];

  const popularQuizzes = [
    { id: 1, title: 'MBTI人格测试', completions: 456, category: '人格测试' },
    { id: 2, title: '情商测试', completions: 234, category: '情商测试' },
    { id: 3, title: '职业兴趣测试', completions: 189, category: '职业测试' },
    { id: 4, title: '恋爱人格测试', completions: 167, category: '恋爱测试' },
    { id: 5, title: '心理健康测试', completions: 123, category: '心理健康' }
  ];

  const recentActivities = [
    { id: 1, user: '张三', action: '完成了MBTI人格测试', time: '2分钟前', type: 'quiz' },
    { id: 2, user: '李四', action: '注册成为新用户', time: '5分钟前', type: 'user' },
    { id: 3, user: '王五', action: '购买了VIP会员', time: '10分钟前', type: 'vip' },
    { id: 4, user: '赵六', action: '完成了情商测试', time: '15分钟前', type: 'quiz' },
    { id: 5, user: '钱七', action: '分享了测评结果', time: '20分钟前', type: 'share' }
  ];

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'quiz': return '#E6D4EF';
      case 'user': return '#D1E5E9';
      case 'vip': return '#FFE4CC';
      case 'share': return '#F8E8F5';
      default: return '#f0f0f0';
    }
  };

  return (
    <div>
      <Title level={2} style={{ marginBottom: '24px' }}>
        仪表盘
      </Title>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        {stats.map((stat, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                prefix={stat.prefix}
                suffix={stat.suffix}
                valueStyle={{ color: '#2c3e50' }}
              />
              <div style={{ marginTop: '8px' }}>
                <Text style={{ fontSize: '12px', color: stat.trendUp ? '#52c41a' : '#ff4d4f' }}>
                  {stat.trendUp ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                  {Math.abs(stat.trend)}%
                </Text>
                <Text style={{ fontSize: '12px', color: '#8c8c8c', marginLeft: '8px' }}>
                  较上周
                </Text>
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      <Row gutter={[16, 16]}>
        {/* 热门测评 */}
        <Col xs={24} lg={12}>
          <Card title="热门测评" style={{ height: '400px' }}>
            <List
              dataSource={popularQuizzes}
              renderItem={(item, index) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar 
                        style={{ 
                          backgroundColor: '#E6D4EF',
                          color: '#fff'
                        }}
                      >
                        {index + 1}
                      </Avatar>
                    }
                    title={item.title}
                    description={
                      <div>
                        <Tag color="blue">{item.category}</Tag>
                        <Text type="secondary">{item.completions} 次完成</Text>
                      </div>
                    }
                  />
                  <div>
                    <Progress
                      percent={Math.round((item.completions / 500) * 100)}
                      size="small"
                      showInfo={false}
                      strokeColor="#E6D4EF"
                    />
                  </div>
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 最近活动 */}
        <Col xs={24} lg={12}>
          <Card title="最近活动" style={{ height: '400px' }}>
            <List
              dataSource={recentActivities}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar 
                        style={{ 
                          backgroundColor: getActivityColor(item.type),
                          color: '#fff'
                        }}
                        icon={<UserOutlined />}
                      />
                    }
                    title={
                      <div>
                        <Text strong>{item.user}</Text>
                        <Text style={{ marginLeft: '8px' }}>{item.action}</Text>
                      </div>
                    }
                    description={
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {item.time}
                      </Text>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
