{"name": "has-flag", "version": "3.0.0", "description": "Check if argv has a specific flag", "license": "MIT", "repository": "sindresorhus/has-flag", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["has", "check", "detect", "contains", "find", "flag", "cli", "command-line", "argv", "process", "arg", "args", "argument", "arguments", "getopt", "minimist", "optimist"], "devDependencies": {"ava": "*", "xo": "*"}}