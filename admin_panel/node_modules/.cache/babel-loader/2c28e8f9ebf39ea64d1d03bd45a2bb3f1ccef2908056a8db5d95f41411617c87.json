{"ast": null, "code": "import useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { useRef } from 'react';\nvar SMOOTH_PTG = 14 / 15;\nexport default function useMobileTouchMove(inVirtual, listRef, callback) {\n  var touchedRef = useRef(false);\n  var touchXRef = useRef(0);\n  var touchYRef = useRef(0);\n  var elementRef = useRef(null);\n\n  // Smooth scroll\n  var intervalRef = useRef(null);\n\n  /* eslint-disable prefer-const */\n  var cleanUpEvents;\n  var onTouchMove = function onTouchMove(e) {\n    if (touchedRef.current) {\n      var currentX = Math.ceil(e.touches[0].pageX);\n      var currentY = Math.ceil(e.touches[0].pageY);\n      var offsetX = touchXRef.current - currentX;\n      var offsetY = touchYRef.current - currentY;\n      var _isHorizontal = Math.abs(offsetX) > Math.abs(offsetY);\n      if (_isHorizontal) {\n        touchXRef.current = currentX;\n      } else {\n        touchYRef.current = currentY;\n      }\n      var scrollHandled = callback(_isHorizontal, _isHorizontal ? offsetX : offsetY, false, e);\n      if (scrollHandled) {\n        e.preventDefault();\n      }\n\n      // Smooth interval\n      clearInterval(intervalRef.current);\n      if (scrollHandled) {\n        intervalRef.current = setInterval(function () {\n          if (_isHorizontal) {\n            offsetX *= SMOOTH_PTG;\n          } else {\n            offsetY *= SMOOTH_PTG;\n          }\n          var offset = Math.floor(_isHorizontal ? offsetX : offsetY);\n          if (!callback(_isHorizontal, offset, true) || Math.abs(offset) <= 0.1) {\n            clearInterval(intervalRef.current);\n          }\n        }, 16);\n      }\n    }\n  };\n  var onTouchEnd = function onTouchEnd() {\n    touchedRef.current = false;\n    cleanUpEvents();\n  };\n  var onTouchStart = function onTouchStart(e) {\n    cleanUpEvents();\n    if (e.touches.length === 1 && !touchedRef.current) {\n      touchedRef.current = true;\n      touchXRef.current = Math.ceil(e.touches[0].pageX);\n      touchYRef.current = Math.ceil(e.touches[0].pageY);\n      elementRef.current = e.target;\n      elementRef.current.addEventListener('touchmove', onTouchMove, {\n        passive: false\n      });\n      elementRef.current.addEventListener('touchend', onTouchEnd, {\n        passive: true\n      });\n    }\n  };\n  cleanUpEvents = function cleanUpEvents() {\n    if (elementRef.current) {\n      elementRef.current.removeEventListener('touchmove', onTouchMove);\n      elementRef.current.removeEventListener('touchend', onTouchEnd);\n    }\n  };\n  useLayoutEffect(function () {\n    if (inVirtual) {\n      listRef.current.addEventListener('touchstart', onTouchStart, {\n        passive: true\n      });\n    }\n    return function () {\n      var _listRef$current;\n      (_listRef$current = listRef.current) === null || _listRef$current === void 0 || _listRef$current.removeEventListener('touchstart', onTouchStart);\n      cleanUpEvents();\n      clearInterval(intervalRef.current);\n    };\n  }, [inVirtual]);\n}", "map": {"version": 3, "names": ["useLayoutEffect", "useRef", "SMOOTH_PTG", "useMobileTouchMove", "inVirtual", "listRef", "callback", "touchedRef", "touchXRef", "touchYRef", "elementRef", "intervalRef", "cleanUpEvents", "onTouchMove", "e", "current", "currentX", "Math", "ceil", "touches", "pageX", "currentY", "pageY", "offsetX", "offsetY", "_isHorizontal", "abs", "scrollHandled", "preventDefault", "clearInterval", "setInterval", "offset", "floor", "onTouchEnd", "onTouchStart", "length", "target", "addEventListener", "passive", "removeEventListener", "_listRef$current"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/rc-virtual-list/es/hooks/useMobileTouchMove.js"], "sourcesContent": ["import useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { useRef } from 'react';\nvar SMOOTH_PTG = 14 / 15;\nexport default function useMobileTouchMove(inVirtual, listRef, callback) {\n  var touchedRef = useRef(false);\n  var touchXRef = useRef(0);\n  var touchYRef = useRef(0);\n  var elementRef = useRef(null);\n\n  // Smooth scroll\n  var intervalRef = useRef(null);\n\n  /* eslint-disable prefer-const */\n  var cleanUpEvents;\n  var onTouchMove = function onTouchMove(e) {\n    if (touchedRef.current) {\n      var currentX = Math.ceil(e.touches[0].pageX);\n      var currentY = Math.ceil(e.touches[0].pageY);\n      var offsetX = touchXRef.current - currentX;\n      var offsetY = touchYRef.current - currentY;\n      var _isHorizontal = Math.abs(offsetX) > Math.abs(offsetY);\n      if (_isHorizontal) {\n        touchXRef.current = currentX;\n      } else {\n        touchYRef.current = currentY;\n      }\n      var scrollHandled = callback(_isHorizontal, _isHorizontal ? offsetX : offsetY, false, e);\n      if (scrollHandled) {\n        e.preventDefault();\n      }\n\n      // Smooth interval\n      clearInterval(intervalRef.current);\n      if (scrollHandled) {\n        intervalRef.current = setInterval(function () {\n          if (_isHorizontal) {\n            offsetX *= SMOOTH_PTG;\n          } else {\n            offsetY *= SMOOTH_PTG;\n          }\n          var offset = Math.floor(_isHorizontal ? offsetX : offsetY);\n          if (!callback(_isHorizontal, offset, true) || Math.abs(offset) <= 0.1) {\n            clearInterval(intervalRef.current);\n          }\n        }, 16);\n      }\n    }\n  };\n  var onTouchEnd = function onTouchEnd() {\n    touchedRef.current = false;\n    cleanUpEvents();\n  };\n  var onTouchStart = function onTouchStart(e) {\n    cleanUpEvents();\n    if (e.touches.length === 1 && !touchedRef.current) {\n      touchedRef.current = true;\n      touchXRef.current = Math.ceil(e.touches[0].pageX);\n      touchYRef.current = Math.ceil(e.touches[0].pageY);\n      elementRef.current = e.target;\n      elementRef.current.addEventListener('touchmove', onTouchMove, {\n        passive: false\n      });\n      elementRef.current.addEventListener('touchend', onTouchEnd, {\n        passive: true\n      });\n    }\n  };\n  cleanUpEvents = function cleanUpEvents() {\n    if (elementRef.current) {\n      elementRef.current.removeEventListener('touchmove', onTouchMove);\n      elementRef.current.removeEventListener('touchend', onTouchEnd);\n    }\n  };\n  useLayoutEffect(function () {\n    if (inVirtual) {\n      listRef.current.addEventListener('touchstart', onTouchStart, {\n        passive: true\n      });\n    }\n    return function () {\n      var _listRef$current;\n      (_listRef$current = listRef.current) === null || _listRef$current === void 0 || _listRef$current.removeEventListener('touchstart', onTouchStart);\n      cleanUpEvents();\n      clearInterval(intervalRef.current);\n    };\n  }, [inVirtual]);\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,kCAAkC;AAC9D,SAASC,MAAM,QAAQ,OAAO;AAC9B,IAAIC,UAAU,GAAG,EAAE,GAAG,EAAE;AACxB,eAAe,SAASC,kBAAkBA,CAACC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EACvE,IAAIC,UAAU,GAAGN,MAAM,CAAC,KAAK,CAAC;EAC9B,IAAIO,SAAS,GAAGP,MAAM,CAAC,CAAC,CAAC;EACzB,IAAIQ,SAAS,GAAGR,MAAM,CAAC,CAAC,CAAC;EACzB,IAAIS,UAAU,GAAGT,MAAM,CAAC,IAAI,CAAC;;EAE7B;EACA,IAAIU,WAAW,GAAGV,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,IAAIW,aAAa;EACjB,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,CAAC,EAAE;IACxC,IAAIP,UAAU,CAACQ,OAAO,EAAE;MACtB,IAAIC,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACJ,CAAC,CAACK,OAAO,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;MAC5C,IAAIC,QAAQ,GAAGJ,IAAI,CAACC,IAAI,CAACJ,CAAC,CAACK,OAAO,CAAC,CAAC,CAAC,CAACG,KAAK,CAAC;MAC5C,IAAIC,OAAO,GAAGf,SAAS,CAACO,OAAO,GAAGC,QAAQ;MAC1C,IAAIQ,OAAO,GAAGf,SAAS,CAACM,OAAO,GAAGM,QAAQ;MAC1C,IAAII,aAAa,GAAGR,IAAI,CAACS,GAAG,CAACH,OAAO,CAAC,GAAGN,IAAI,CAACS,GAAG,CAACF,OAAO,CAAC;MACzD,IAAIC,aAAa,EAAE;QACjBjB,SAAS,CAACO,OAAO,GAAGC,QAAQ;MAC9B,CAAC,MAAM;QACLP,SAAS,CAACM,OAAO,GAAGM,QAAQ;MAC9B;MACA,IAAIM,aAAa,GAAGrB,QAAQ,CAACmB,aAAa,EAAEA,aAAa,GAAGF,OAAO,GAAGC,OAAO,EAAE,KAAK,EAAEV,CAAC,CAAC;MACxF,IAAIa,aAAa,EAAE;QACjBb,CAAC,CAACc,cAAc,CAAC,CAAC;MACpB;;MAEA;MACAC,aAAa,CAAClB,WAAW,CAACI,OAAO,CAAC;MAClC,IAAIY,aAAa,EAAE;QACjBhB,WAAW,CAACI,OAAO,GAAGe,WAAW,CAAC,YAAY;UAC5C,IAAIL,aAAa,EAAE;YACjBF,OAAO,IAAIrB,UAAU;UACvB,CAAC,MAAM;YACLsB,OAAO,IAAItB,UAAU;UACvB;UACA,IAAI6B,MAAM,GAAGd,IAAI,CAACe,KAAK,CAACP,aAAa,GAAGF,OAAO,GAAGC,OAAO,CAAC;UAC1D,IAAI,CAAClB,QAAQ,CAACmB,aAAa,EAAEM,MAAM,EAAE,IAAI,CAAC,IAAId,IAAI,CAACS,GAAG,CAACK,MAAM,CAAC,IAAI,GAAG,EAAE;YACrEF,aAAa,CAAClB,WAAW,CAACI,OAAO,CAAC;UACpC;QACF,CAAC,EAAE,EAAE,CAAC;MACR;IACF;EACF,CAAC;EACD,IAAIkB,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrC1B,UAAU,CAACQ,OAAO,GAAG,KAAK;IAC1BH,aAAa,CAAC,CAAC;EACjB,CAAC;EACD,IAAIsB,YAAY,GAAG,SAASA,YAAYA,CAACpB,CAAC,EAAE;IAC1CF,aAAa,CAAC,CAAC;IACf,IAAIE,CAAC,CAACK,OAAO,CAACgB,MAAM,KAAK,CAAC,IAAI,CAAC5B,UAAU,CAACQ,OAAO,EAAE;MACjDR,UAAU,CAACQ,OAAO,GAAG,IAAI;MACzBP,SAAS,CAACO,OAAO,GAAGE,IAAI,CAACC,IAAI,CAACJ,CAAC,CAACK,OAAO,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;MACjDX,SAAS,CAACM,OAAO,GAAGE,IAAI,CAACC,IAAI,CAACJ,CAAC,CAACK,OAAO,CAAC,CAAC,CAAC,CAACG,KAAK,CAAC;MACjDZ,UAAU,CAACK,OAAO,GAAGD,CAAC,CAACsB,MAAM;MAC7B1B,UAAU,CAACK,OAAO,CAACsB,gBAAgB,CAAC,WAAW,EAAExB,WAAW,EAAE;QAC5DyB,OAAO,EAAE;MACX,CAAC,CAAC;MACF5B,UAAU,CAACK,OAAO,CAACsB,gBAAgB,CAAC,UAAU,EAAEJ,UAAU,EAAE;QAC1DK,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF,CAAC;EACD1B,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IACvC,IAAIF,UAAU,CAACK,OAAO,EAAE;MACtBL,UAAU,CAACK,OAAO,CAACwB,mBAAmB,CAAC,WAAW,EAAE1B,WAAW,CAAC;MAChEH,UAAU,CAACK,OAAO,CAACwB,mBAAmB,CAAC,UAAU,EAAEN,UAAU,CAAC;IAChE;EACF,CAAC;EACDjC,eAAe,CAAC,YAAY;IAC1B,IAAII,SAAS,EAAE;MACbC,OAAO,CAACU,OAAO,CAACsB,gBAAgB,CAAC,YAAY,EAAEH,YAAY,EAAE;QAC3DI,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;IACA,OAAO,YAAY;MACjB,IAAIE,gBAAgB;MACpB,CAACA,gBAAgB,GAAGnC,OAAO,CAACU,OAAO,MAAM,IAAI,IAAIyB,gBAAgB,KAAK,KAAK,CAAC,IAAIA,gBAAgB,CAACD,mBAAmB,CAAC,YAAY,EAAEL,YAAY,CAAC;MAChJtB,aAAa,CAAC,CAAC;MACfiB,aAAa,CAAClB,WAAW,CAACI,OAAO,CAAC;IACpC,CAAC;EACH,CAAC,EAAE,CAACX,SAAS,CAAC,CAAC;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}