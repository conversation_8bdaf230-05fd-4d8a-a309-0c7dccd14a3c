{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport React from 'react';\nvar Handler = function Handler(_ref) {\n  var _ref$size = _ref.size,\n    size = _ref$size === void 0 ? 'default' : _ref$size,\n    color = _ref.color,\n    prefixCls = _ref.prefixCls;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-handler\"), _defineProperty({}, \"\".concat(prefixCls, \"-handler-sm\"), size === 'small')),\n    style: {\n      backgroundColor: color\n    }\n  });\n};\nexport default Handler;", "map": {"version": 3, "names": ["_defineProperty", "classNames", "React", "Handler", "_ref", "_ref$size", "size", "color", "prefixCls", "createElement", "className", "concat", "style", "backgroundColor"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/@rc-component/color-picker/es/components/Handler.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport React from 'react';\nvar Handler = function Handler(_ref) {\n  var _ref$size = _ref.size,\n    size = _ref$size === void 0 ? 'default' : _ref$size,\n    color = _ref.color,\n    prefixCls = _ref.prefixCls;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-handler\"), _defineProperty({}, \"\".concat(prefixCls, \"-handler-sm\"), size === 'small')),\n    style: {\n      backgroundColor: color\n    }\n  });\n};\nexport default Handler;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,MAAM,OAAO;AACzB,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,IAAI,EAAE;EACnC,IAAIC,SAAS,GAAGD,IAAI,CAACE,IAAI;IACvBA,IAAI,GAAGD,SAAS,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGA,SAAS;IACnDE,KAAK,GAAGH,IAAI,CAACG,KAAK;IAClBC,SAAS,GAAGJ,IAAI,CAACI,SAAS;EAC5B,OAAO,aAAaN,KAAK,CAACO,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAET,UAAU,CAAC,EAAE,CAACU,MAAM,CAACH,SAAS,EAAE,UAAU,CAAC,EAAER,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACW,MAAM,CAACH,SAAS,EAAE,aAAa,CAAC,EAAEF,IAAI,KAAK,OAAO,CAAC,CAAC;IACnIM,KAAK,EAAE;MACLC,eAAe,EAAEN;IACnB;EACF,CAAC,CAAC;AACJ,CAAC;AACD,eAAeJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}