{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nexport default function useHover() {\n  var _React$useState = React.useState(-1),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    startRow = _React$useState2[0],\n    setStartRow = _React$useState2[1];\n  var _React$useState3 = React.useState(-1),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    endRow = _React$useState4[0],\n    setEndRow = _React$useState4[1];\n  var onHover = React.useCallback(function (start, end) {\n    setStartRow(start);\n    setEndRow(end);\n  }, []);\n  return [startRow, endRow, onHover];\n}", "map": {"version": 3, "names": ["_slicedToArray", "React", "useHover", "_React$useState", "useState", "_React$useState2", "startRow", "setStartRow", "_React$useState3", "_React$useState4", "endRow", "setEndRow", "onHover", "useCallback", "start", "end"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/rc-table/es/hooks/useHover.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nexport default function useHover() {\n  var _React$useState = React.useState(-1),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    startRow = _React$useState2[0],\n    setStartRow = _React$useState2[1];\n  var _React$useState3 = React.useState(-1),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    endRow = _React$useState4[0],\n    setEndRow = _React$useState4[1];\n  var onHover = React.useCallback(function (start, end) {\n    setStartRow(start);\n    setEndRow(end);\n  }, []);\n  return [startRow, endRow, onHover];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,QAAQA,CAAA,EAAG;EACjC,IAAIC,eAAe,GAAGF,KAAK,CAACG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACtCC,gBAAgB,GAAGL,cAAc,CAACG,eAAe,EAAE,CAAC,CAAC;IACrDG,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC9BE,WAAW,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACnC,IAAIG,gBAAgB,GAAGP,KAAK,CAACG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACvCK,gBAAgB,GAAGT,cAAc,CAACQ,gBAAgB,EAAE,CAAC,CAAC;IACtDE,MAAM,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC5BE,SAAS,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACjC,IAAIG,OAAO,GAAGX,KAAK,CAACY,WAAW,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;IACpDR,WAAW,CAACO,KAAK,CAAC;IAClBH,SAAS,CAACI,GAAG,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACT,QAAQ,EAAEI,MAAM,EAAEE,OAAO,CAAC;AACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}