{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { operationUnit } from '../../style';\nconst genExpandStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    motionDurationSlow,\n    lineWidth,\n    paddingXS,\n    lineType,\n    tableBorderColor,\n    tableExpandIconBg,\n    tableExpandColumnWidth,\n    borderRadius,\n    tablePaddingVertical,\n    tablePaddingHorizontal,\n    tableExpandedRowBg,\n    paddingXXS,\n    expandIconMarginTop,\n    expandIconSize,\n    expandIconHalfInner,\n    expandIconScale,\n    calc\n  } = token;\n  const tableBorder = `${unit(lineWidth)} ${lineType} ${tableBorderColor}`;\n  const expandIconLineOffset = calc(paddingXXS).sub(lineWidth).equal();\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-expand-icon-col`]: {\n        width: tableExpandColumnWidth\n      },\n      [`${componentCls}-row-expand-icon-cell`]: {\n        textAlign: 'center',\n        [`${componentCls}-row-expand-icon`]: {\n          display: 'inline-flex',\n          float: 'none',\n          verticalAlign: 'sub'\n        }\n      },\n      [`${componentCls}-row-indent`]: {\n        height: 1,\n        float: 'left'\n      },\n      [`${componentCls}-row-expand-icon`]: Object.assign(Object.assign({}, operationUnit(token)), {\n        position: 'relative',\n        float: 'left',\n        width: expandIconSize,\n        height: expandIconSize,\n        color: 'inherit',\n        lineHeight: unit(expandIconSize),\n        background: tableExpandIconBg,\n        border: tableBorder,\n        borderRadius,\n        transform: `scale(${expandIconScale})`,\n        '&:focus, &:hover, &:active': {\n          borderColor: 'currentcolor'\n        },\n        '&::before, &::after': {\n          position: 'absolute',\n          background: 'currentcolor',\n          transition: `transform ${motionDurationSlow} ease-out`,\n          content: '\"\"'\n        },\n        '&::before': {\n          top: expandIconHalfInner,\n          insetInlineEnd: expandIconLineOffset,\n          insetInlineStart: expandIconLineOffset,\n          height: lineWidth\n        },\n        '&::after': {\n          top: expandIconLineOffset,\n          bottom: expandIconLineOffset,\n          insetInlineStart: expandIconHalfInner,\n          width: lineWidth,\n          transform: 'rotate(90deg)'\n        },\n        // Motion effect\n        '&-collapsed::before': {\n          transform: 'rotate(-180deg)'\n        },\n        '&-collapsed::after': {\n          transform: 'rotate(0deg)'\n        },\n        '&-spaced': {\n          '&::before, &::after': {\n            display: 'none',\n            content: 'none'\n          },\n          background: 'transparent',\n          border: 0,\n          visibility: 'hidden'\n        }\n      }),\n      [`${componentCls}-row-indent + ${componentCls}-row-expand-icon`]: {\n        marginTop: expandIconMarginTop,\n        marginInlineEnd: paddingXS\n      },\n      [`tr${componentCls}-expanded-row`]: {\n        '&, &:hover': {\n          '> th, > td': {\n            background: tableExpandedRowBg\n          }\n        },\n        // https://github.com/ant-design/ant-design/issues/25573\n        [`${antCls}-descriptions-view`]: {\n          display: 'flex',\n          table: {\n            flex: 'auto',\n            width: '100%'\n          }\n        }\n      },\n      // With fixed\n      [`${componentCls}-expanded-row-fixed`]: {\n        position: 'relative',\n        margin: `${unit(calc(tablePaddingVertical).mul(-1).equal())} ${unit(calc(tablePaddingHorizontal).mul(-1).equal())}`,\n        padding: `${unit(tablePaddingVertical)} ${unit(tablePaddingHorizontal)}`\n      }\n    }\n  };\n};\nexport default genExpandStyle;", "map": {"version": 3, "names": ["unit", "operationUnit", "genExpandStyle", "token", "componentCls", "antCls", "motionDurationSlow", "lineWidth", "paddingXS", "lineType", "tableBorderColor", "tableExpandIconBg", "tableExpandColumnWidth", "borderRadius", "tablePaddingVertical", "tablePaddingHorizontal", "tableExpandedRowBg", "paddingXXS", "expandIconMarginTop", "expandIconSize", "expandIconHalfInner", "expandIconScale", "calc", "tableBorder", "expandIconLineOffset", "sub", "equal", "width", "textAlign", "display", "float", "verticalAlign", "height", "Object", "assign", "position", "color", "lineHeight", "background", "border", "transform", "borderColor", "transition", "content", "top", "insetInlineEnd", "insetInlineStart", "bottom", "visibility", "marginTop", "marginInlineEnd", "table", "flex", "margin", "mul", "padding"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/antd/es/table/style/expand.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { operationUnit } from '../../style';\nconst genExpandStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    motionDurationSlow,\n    lineWidth,\n    paddingXS,\n    lineType,\n    tableBorderColor,\n    tableExpandIconBg,\n    tableExpandColumnWidth,\n    borderRadius,\n    tablePaddingVertical,\n    tablePaddingHorizontal,\n    tableExpandedRowBg,\n    paddingXXS,\n    expandIconMarginTop,\n    expandIconSize,\n    expandIconHalfInner,\n    expandIconScale,\n    calc\n  } = token;\n  const tableBorder = `${unit(lineWidth)} ${lineType} ${tableBorderColor}`;\n  const expandIconLineOffset = calc(paddingXXS).sub(lineWidth).equal();\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-expand-icon-col`]: {\n        width: tableExpandColumnWidth\n      },\n      [`${componentCls}-row-expand-icon-cell`]: {\n        textAlign: 'center',\n        [`${componentCls}-row-expand-icon`]: {\n          display: 'inline-flex',\n          float: 'none',\n          verticalAlign: 'sub'\n        }\n      },\n      [`${componentCls}-row-indent`]: {\n        height: 1,\n        float: 'left'\n      },\n      [`${componentCls}-row-expand-icon`]: Object.assign(Object.assign({}, operationUnit(token)), {\n        position: 'relative',\n        float: 'left',\n        width: expandIconSize,\n        height: expandIconSize,\n        color: 'inherit',\n        lineHeight: unit(expandIconSize),\n        background: tableExpandIconBg,\n        border: tableBorder,\n        borderRadius,\n        transform: `scale(${expandIconScale})`,\n        '&:focus, &:hover, &:active': {\n          borderColor: 'currentcolor'\n        },\n        '&::before, &::after': {\n          position: 'absolute',\n          background: 'currentcolor',\n          transition: `transform ${motionDurationSlow} ease-out`,\n          content: '\"\"'\n        },\n        '&::before': {\n          top: expandIconHalfInner,\n          insetInlineEnd: expandIconLineOffset,\n          insetInlineStart: expandIconLineOffset,\n          height: lineWidth\n        },\n        '&::after': {\n          top: expandIconLineOffset,\n          bottom: expandIconLineOffset,\n          insetInlineStart: expandIconHalfInner,\n          width: lineWidth,\n          transform: 'rotate(90deg)'\n        },\n        // Motion effect\n        '&-collapsed::before': {\n          transform: 'rotate(-180deg)'\n        },\n        '&-collapsed::after': {\n          transform: 'rotate(0deg)'\n        },\n        '&-spaced': {\n          '&::before, &::after': {\n            display: 'none',\n            content: 'none'\n          },\n          background: 'transparent',\n          border: 0,\n          visibility: 'hidden'\n        }\n      }),\n      [`${componentCls}-row-indent + ${componentCls}-row-expand-icon`]: {\n        marginTop: expandIconMarginTop,\n        marginInlineEnd: paddingXS\n      },\n      [`tr${componentCls}-expanded-row`]: {\n        '&, &:hover': {\n          '> th, > td': {\n            background: tableExpandedRowBg\n          }\n        },\n        // https://github.com/ant-design/ant-design/issues/25573\n        [`${antCls}-descriptions-view`]: {\n          display: 'flex',\n          table: {\n            flex: 'auto',\n            width: '100%'\n          }\n        }\n      },\n      // With fixed\n      [`${componentCls}-expanded-row-fixed`]: {\n        position: 'relative',\n        margin: `${unit(calc(tablePaddingVertical).mul(-1).equal())} ${unit(calc(tablePaddingHorizontal).mul(-1).equal())}`,\n        padding: `${unit(tablePaddingVertical)} ${unit(tablePaddingHorizontal)}`\n      }\n    }\n  };\n};\nexport default genExpandStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,aAAa,QAAQ,aAAa;AAC3C,MAAMC,cAAc,GAAGC,KAAK,IAAI;EAC9B,MAAM;IACJC,YAAY;IACZC,MAAM;IACNC,kBAAkB;IAClBC,SAAS;IACTC,SAAS;IACTC,QAAQ;IACRC,gBAAgB;IAChBC,iBAAiB;IACjBC,sBAAsB;IACtBC,YAAY;IACZC,oBAAoB;IACpBC,sBAAsB;IACtBC,kBAAkB;IAClBC,UAAU;IACVC,mBAAmB;IACnBC,cAAc;IACdC,mBAAmB;IACnBC,eAAe;IACfC;EACF,CAAC,GAAGnB,KAAK;EACT,MAAMoB,WAAW,GAAG,GAAGvB,IAAI,CAACO,SAAS,CAAC,IAAIE,QAAQ,IAAIC,gBAAgB,EAAE;EACxE,MAAMc,oBAAoB,GAAGF,IAAI,CAACL,UAAU,CAAC,CAACQ,GAAG,CAAClB,SAAS,CAAC,CAACmB,KAAK,CAAC,CAAC;EACpE,OAAO;IACL,CAAC,GAAGtB,YAAY,UAAU,GAAG;MAC3B,CAAC,GAAGA,YAAY,kBAAkB,GAAG;QACnCuB,KAAK,EAAEf;MACT,CAAC;MACD,CAAC,GAAGR,YAAY,uBAAuB,GAAG;QACxCwB,SAAS,EAAE,QAAQ;QACnB,CAAC,GAAGxB,YAAY,kBAAkB,GAAG;UACnCyB,OAAO,EAAE,aAAa;UACtBC,KAAK,EAAE,MAAM;UACbC,aAAa,EAAE;QACjB;MACF,CAAC;MACD,CAAC,GAAG3B,YAAY,aAAa,GAAG;QAC9B4B,MAAM,EAAE,CAAC;QACTF,KAAK,EAAE;MACT,CAAC;MACD,CAAC,GAAG1B,YAAY,kBAAkB,GAAG6B,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjC,aAAa,CAACE,KAAK,CAAC,CAAC,EAAE;QAC1FgC,QAAQ,EAAE,UAAU;QACpBL,KAAK,EAAE,MAAM;QACbH,KAAK,EAAER,cAAc;QACrBa,MAAM,EAAEb,cAAc;QACtBiB,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAErC,IAAI,CAACmB,cAAc,CAAC;QAChCmB,UAAU,EAAE3B,iBAAiB;QAC7B4B,MAAM,EAAEhB,WAAW;QACnBV,YAAY;QACZ2B,SAAS,EAAE,SAASnB,eAAe,GAAG;QACtC,4BAA4B,EAAE;UAC5BoB,WAAW,EAAE;QACf,CAAC;QACD,qBAAqB,EAAE;UACrBN,QAAQ,EAAE,UAAU;UACpBG,UAAU,EAAE,cAAc;UAC1BI,UAAU,EAAE,aAAapC,kBAAkB,WAAW;UACtDqC,OAAO,EAAE;QACX,CAAC;QACD,WAAW,EAAE;UACXC,GAAG,EAAExB,mBAAmB;UACxByB,cAAc,EAAErB,oBAAoB;UACpCsB,gBAAgB,EAAEtB,oBAAoB;UACtCQ,MAAM,EAAEzB;QACV,CAAC;QACD,UAAU,EAAE;UACVqC,GAAG,EAAEpB,oBAAoB;UACzBuB,MAAM,EAAEvB,oBAAoB;UAC5BsB,gBAAgB,EAAE1B,mBAAmB;UACrCO,KAAK,EAAEpB,SAAS;UAChBiC,SAAS,EAAE;QACb,CAAC;QACD;QACA,qBAAqB,EAAE;UACrBA,SAAS,EAAE;QACb,CAAC;QACD,oBAAoB,EAAE;UACpBA,SAAS,EAAE;QACb,CAAC;QACD,UAAU,EAAE;UACV,qBAAqB,EAAE;YACrBX,OAAO,EAAE,MAAM;YACfc,OAAO,EAAE;UACX,CAAC;UACDL,UAAU,EAAE,aAAa;UACzBC,MAAM,EAAE,CAAC;UACTS,UAAU,EAAE;QACd;MACF,CAAC,CAAC;MACF,CAAC,GAAG5C,YAAY,iBAAiBA,YAAY,kBAAkB,GAAG;QAChE6C,SAAS,EAAE/B,mBAAmB;QAC9BgC,eAAe,EAAE1C;MACnB,CAAC;MACD,CAAC,KAAKJ,YAAY,eAAe,GAAG;QAClC,YAAY,EAAE;UACZ,YAAY,EAAE;YACZkC,UAAU,EAAEtB;UACd;QACF,CAAC;QACD;QACA,CAAC,GAAGX,MAAM,oBAAoB,GAAG;UAC/BwB,OAAO,EAAE,MAAM;UACfsB,KAAK,EAAE;YACLC,IAAI,EAAE,MAAM;YACZzB,KAAK,EAAE;UACT;QACF;MACF,CAAC;MACD;MACA,CAAC,GAAGvB,YAAY,qBAAqB,GAAG;QACtC+B,QAAQ,EAAE,UAAU;QACpBkB,MAAM,EAAE,GAAGrD,IAAI,CAACsB,IAAI,CAACR,oBAAoB,CAAC,CAACwC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC5B,KAAK,CAAC,CAAC,CAAC,IAAI1B,IAAI,CAACsB,IAAI,CAACP,sBAAsB,CAAC,CAACuC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC5B,KAAK,CAAC,CAAC,CAAC,EAAE;QACnH6B,OAAO,EAAE,GAAGvD,IAAI,CAACc,oBAAoB,CAAC,IAAId,IAAI,CAACe,sBAAsB,CAAC;MACxE;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeb,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}