{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport UngroupOutlinedSvg from \"@ant-design/icons-svg/es/asn/UngroupOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar UngroupOutlined = function UngroupOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: UngroupOutlinedSvg\n  }));\n};\n\n/**![ungroup](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik03MzYgNTUwSDI4OGMtOC44IDAtMTYgNy4yLTE2IDE2djE3NmMwIDguOCA3LjIgMTYgMTYgMTZoNDQ4YzguOCAwIDE2LTcuMiAxNi0xNlY1NjZjMC04LjgtNy4yLTE2LTE2LTE2em0tNTYgMTM2SDM0NHYtNjRoMzM2djY0em0yMDggMTMwYy0zOS44IDAtNzIgMzIuMi03MiA3MnMzMi4yIDcyIDcyIDcyIDcyLTMyLjIgNzItNzItMzIuMi03Mi03Mi03MnptMCA5NmMtMTMuMyAwLTI0LTEwLjctMjQtMjRzMTAuNy0yNCAyNC0yNCAyNCAxMC43IDI0IDI0LTEwLjcgMjQtMjQgMjR6TTczNiAyNjZIMjg4Yy04LjggMC0xNiA3LjItMTYgMTZ2MTc2YzAgOC44IDcuMiAxNiAxNiAxNmg0NDhjOC44IDAgMTYtNy4yIDE2LTE2VjI4MmMwLTguOC03LjItMTYtMTYtMTZ6bS01NiAxMzZIMzQ0di02NGgzMzZ2NjR6bTIwOC0xOTRjMzkuOCAwIDcyLTMyLjIgNzItNzJzLTMyLjItNzItNzItNzItNzIgMzIuMi03MiA3MiAzMi4yIDcyIDcyIDcyem0wLTk2YzEzLjMgMCAyNCAxMC43IDI0IDI0cy0xMC43IDI0LTI0IDI0LTI0LTEwLjctMjQtMjQgMTAuNy0yNCAyNC0yNHpNMTM2IDY0Yy0zOS44IDAtNzIgMzIuMi03MiA3MnMzMi4yIDcyIDcyIDcyIDcyLTMyLjIgNzItNzItMzIuMi03Mi03Mi03MnptMCA5NmMtMTMuMyAwLTI0LTEwLjctMjQtMjRzMTAuNy0yNCAyNC0yNCAyNCAxMC43IDI0IDI0LTEwLjcgMjQtMjQgMjR6bTAgNjU2Yy0zOS44IDAtNzIgMzIuMi03MiA3MnMzMi4yIDcyIDcyIDcyIDcyLTMyLjIgNzItNzItMzIuMi03Mi03Mi03MnptMCA5NmMtMTMuMyAwLTI0LTEwLjctMjQtMjRzMTAuNy0yNCAyNC0yNCAyNCAxMC43IDI0IDI0LTEwLjcgMjQtMjQgMjR6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(UngroupOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'UngroupOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "UngroupOutlinedSvg", "AntdIcon", "UngroupOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/antd/node_modules/@ant-design/icons/es/icons/UngroupOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport UngroupOutlinedSvg from \"@ant-design/icons-svg/es/asn/UngroupOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar UngroupOutlined = function UngroupOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: UngroupOutlinedSvg\n  }));\n};\n\n/**![ungroup](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik03MzYgNTUwSDI4OGMtOC44IDAtMTYgNy4yLTE2IDE2djE3NmMwIDguOCA3LjIgMTYgMTYgMTZoNDQ4YzguOCAwIDE2LTcuMiAxNi0xNlY1NjZjMC04LjgtNy4yLTE2LTE2LTE2em0tNTYgMTM2SDM0NHYtNjRoMzM2djY0em0yMDggMTMwYy0zOS44IDAtNzIgMzIuMi03MiA3MnMzMi4yIDcyIDcyIDcyIDcyLTMyLjIgNzItNzItMzIuMi03Mi03Mi03MnptMCA5NmMtMTMuMyAwLTI0LTEwLjctMjQtMjRzMTAuNy0yNCAyNC0yNCAyNCAxMC43IDI0IDI0LTEwLjcgMjQtMjQgMjR6TTczNiAyNjZIMjg4Yy04LjggMC0xNiA3LjItMTYgMTZ2MTc2YzAgOC44IDcuMiAxNiAxNiAxNmg0NDhjOC44IDAgMTYtNy4yIDE2LTE2VjI4MmMwLTguOC03LjItMTYtMTYtMTZ6bS01NiAxMzZIMzQ0di02NGgzMzZ2NjR6bTIwOC0xOTRjMzkuOCAwIDcyLTMyLjIgNzItNzJzLTMyLjItNzItNzItNzItNzIgMzIuMi03MiA3MiAzMi4yIDcyIDcyIDcyem0wLTk2YzEzLjMgMCAyNCAxMC43IDI0IDI0cy0xMC43IDI0LTI0IDI0LTI0LTEwLjctMjQtMjQgMTAuNy0yNCAyNC0yNHpNMTM2IDY0Yy0zOS44IDAtNzIgMzIuMi03MiA3MnMzMi4yIDcyIDcyIDcyIDcyLTMyLjIgNzItNzItMzIuMi03Mi03Mi03MnptMCA5NmMtMTMuMyAwLTI0LTEwLjctMjQtMjRzMTAuNy0yNCAyNC0yNCAyNCAxMC43IDI0IDI0LTEwLjcgMjQtMjQgMjR6bTAgNjU2Yy0zOS44IDAtNzIgMzIuMi03MiA3MnMzMi4yIDcyIDcyIDcyIDcyLTMyLjIgNzItNzItMzIuMi03Mi03Mi03MnptMCA5NmMtMTMuMyAwLTI0LTEwLjctMjQtMjRzMTAuNy0yNCAyNC0yNCAyNCAxMC43IDI0IDI0LTEwLjcgMjQtMjQgMjR6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(UngroupOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'UngroupOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACzD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,eAAe,CAAC;AAC5D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,iBAAiB;AACzC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}