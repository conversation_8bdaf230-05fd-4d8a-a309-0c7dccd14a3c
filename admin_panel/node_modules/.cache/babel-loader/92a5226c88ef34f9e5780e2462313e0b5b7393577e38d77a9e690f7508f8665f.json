{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport classnames from 'classnames';\n// ========================= ClassNames =========================\nexport function mergeClassNames(schema, ...classNames) {\n  const mergedSchema = schema || {};\n  return classNames.reduce((acc, cur) => {\n    // Loop keys of the current classNames\n    Object.keys(cur || {}).forEach(key => {\n      const keySchema = mergedSchema[key];\n      const curVal = cur[key];\n      if (keySchema && typeof keySchema === 'object') {\n        if (curVal && typeof curVal === 'object') {\n          // Loop fill\n          acc[key] = mergeClassNames(keySchema, acc[key], curVal);\n        } else {\n          // Covert string to object structure\n          const {\n            _default: defaultField\n          } = keySchema;\n          acc[key] = acc[key] || {};\n          acc[key][defaultField] = classnames(acc[key][defaultField], curVal);\n        }\n      } else {\n        // Flatten fill\n        acc[key] = classnames(acc[key], curVal);\n      }\n    });\n    return acc;\n  }, {});\n}\nfunction useSemanticClassNames(schema, ...classNames) {\n  return React.useMemo(() => mergeClassNames.apply(void 0, [schema].concat(classNames)), [classNames]);\n}\n// =========================== Styles ===========================\nfunction useSemanticStyles(...styles) {\n  return React.useMemo(() => {\n    return styles.reduce((acc, cur = {}) => {\n      Object.keys(cur).forEach(key => {\n        acc[key] = Object.assign(Object.assign({}, acc[key]), cur[key]);\n      });\n      return acc;\n    }, {});\n  }, [styles]);\n}\n// =========================== Export ===========================\nfunction fillObjectBySchema(obj, schema) {\n  const newObj = Object.assign({}, obj);\n  Object.keys(schema).forEach(key => {\n    if (key !== '_default') {\n      const nestSchema = schema[key];\n      const nextValue = newObj[key] || {};\n      newObj[key] = nestSchema ? fillObjectBySchema(nextValue, nestSchema) : nextValue;\n    }\n  });\n  return newObj;\n}\n/**\n * Merge classNames and styles from multiple sources.\n * When `schema` is provided, it will **must** provide the nest object structure.\n */\nexport default function useMergeSemantic(classNamesList, stylesList, schema) {\n  const mergedClassNames = useSemanticClassNames.apply(void 0, [schema].concat(_toConsumableArray(classNamesList)));\n  const mergedStyles = useSemanticStyles.apply(void 0, _toConsumableArray(stylesList));\n  return React.useMemo(() => {\n    return [fillObjectBySchema(mergedClassNames, schema), fillObjectBySchema(mergedStyles, schema)];\n  }, [mergedClassNames, mergedStyles]);\n}", "map": {"version": 3, "names": ["_toConsumableArray", "React", "classnames", "mergeClassNames", "schema", "classNames", "mergedSchema", "reduce", "acc", "cur", "Object", "keys", "for<PERSON>ach", "key", "keySchema", "curVal", "_default", "defaultField", "useSemanticClassNames", "useMemo", "apply", "concat", "useSemanticStyles", "styles", "assign", "fillObjectBySchema", "obj", "newObj", "nestSchema", "nextValue", "useMergeSemantic", "classNamesList", "stylesList", "mergedClassNames", "mergedStyles"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/antd/es/_util/hooks/useMergeSemantic/index.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport classnames from 'classnames';\n// ========================= ClassNames =========================\nexport function mergeClassNames(schema, ...classNames) {\n  const mergedSchema = schema || {};\n  return classNames.reduce((acc, cur) => {\n    // Loop keys of the current classNames\n    Object.keys(cur || {}).forEach(key => {\n      const keySchema = mergedSchema[key];\n      const curVal = cur[key];\n      if (keySchema && typeof keySchema === 'object') {\n        if (curVal && typeof curVal === 'object') {\n          // Loop fill\n          acc[key] = mergeClassNames(keySchema, acc[key], curVal);\n        } else {\n          // Covert string to object structure\n          const {\n            _default: defaultField\n          } = keySchema;\n          acc[key] = acc[key] || {};\n          acc[key][defaultField] = classnames(acc[key][defaultField], curVal);\n        }\n      } else {\n        // Flatten fill\n        acc[key] = classnames(acc[key], curVal);\n      }\n    });\n    return acc;\n  }, {});\n}\nfunction useSemanticClassNames(schema, ...classNames) {\n  return React.useMemo(() => mergeClassNames.apply(void 0, [schema].concat(classNames)), [classNames]);\n}\n// =========================== Styles ===========================\nfunction useSemanticStyles(...styles) {\n  return React.useMemo(() => {\n    return styles.reduce((acc, cur = {}) => {\n      Object.keys(cur).forEach(key => {\n        acc[key] = Object.assign(Object.assign({}, acc[key]), cur[key]);\n      });\n      return acc;\n    }, {});\n  }, [styles]);\n}\n// =========================== Export ===========================\nfunction fillObjectBySchema(obj, schema) {\n  const newObj = Object.assign({}, obj);\n  Object.keys(schema).forEach(key => {\n    if (key !== '_default') {\n      const nestSchema = schema[key];\n      const nextValue = newObj[key] || {};\n      newObj[key] = nestSchema ? fillObjectBySchema(nextValue, nestSchema) : nextValue;\n    }\n  });\n  return newObj;\n}\n/**\n * Merge classNames and styles from multiple sources.\n * When `schema` is provided, it will **must** provide the nest object structure.\n */\nexport default function useMergeSemantic(classNamesList, stylesList, schema) {\n  const mergedClassNames = useSemanticClassNames.apply(void 0, [schema].concat(_toConsumableArray(classNamesList)));\n  const mergedStyles = useSemanticStyles.apply(void 0, _toConsumableArray(stylesList));\n  return React.useMemo(() => {\n    return [fillObjectBySchema(mergedClassNames, schema), fillObjectBySchema(mergedStyles, schema)];\n  }, [mergedClassNames, mergedStyles]);\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC;AACA,OAAO,SAASC,eAAeA,CAACC,MAAM,EAAE,GAAGC,UAAU,EAAE;EACrD,MAAMC,YAAY,GAAGF,MAAM,IAAI,CAAC,CAAC;EACjC,OAAOC,UAAU,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;IACrC;IACAC,MAAM,CAACC,IAAI,CAACF,GAAG,IAAI,CAAC,CAAC,CAAC,CAACG,OAAO,CAACC,GAAG,IAAI;MACpC,MAAMC,SAAS,GAAGR,YAAY,CAACO,GAAG,CAAC;MACnC,MAAME,MAAM,GAAGN,GAAG,CAACI,GAAG,CAAC;MACvB,IAAIC,SAAS,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;QAC9C,IAAIC,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;UACxC;UACAP,GAAG,CAACK,GAAG,CAAC,GAAGV,eAAe,CAACW,SAAS,EAAEN,GAAG,CAACK,GAAG,CAAC,EAAEE,MAAM,CAAC;QACzD,CAAC,MAAM;UACL;UACA,MAAM;YACJC,QAAQ,EAAEC;UACZ,CAAC,GAAGH,SAAS;UACbN,GAAG,CAACK,GAAG,CAAC,GAAGL,GAAG,CAACK,GAAG,CAAC,IAAI,CAAC,CAAC;UACzBL,GAAG,CAACK,GAAG,CAAC,CAACI,YAAY,CAAC,GAAGf,UAAU,CAACM,GAAG,CAACK,GAAG,CAAC,CAACI,YAAY,CAAC,EAAEF,MAAM,CAAC;QACrE;MACF,CAAC,MAAM;QACL;QACAP,GAAG,CAACK,GAAG,CAAC,GAAGX,UAAU,CAACM,GAAG,CAACK,GAAG,CAAC,EAAEE,MAAM,CAAC;MACzC;IACF,CAAC,CAAC;IACF,OAAOP,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;AACA,SAASU,qBAAqBA,CAACd,MAAM,EAAE,GAAGC,UAAU,EAAE;EACpD,OAAOJ,KAAK,CAACkB,OAAO,CAAC,MAAMhB,eAAe,CAACiB,KAAK,CAAC,KAAK,CAAC,EAAE,CAAChB,MAAM,CAAC,CAACiB,MAAM,CAAChB,UAAU,CAAC,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;AACtG;AACA;AACA,SAASiB,iBAAiBA,CAAC,GAAGC,MAAM,EAAE;EACpC,OAAOtB,KAAK,CAACkB,OAAO,CAAC,MAAM;IACzB,OAAOI,MAAM,CAAChB,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,GAAG,CAAC,CAAC,KAAK;MACtCC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAACG,OAAO,CAACC,GAAG,IAAI;QAC9BL,GAAG,CAACK,GAAG,CAAC,GAAGH,MAAM,CAACc,MAAM,CAACd,MAAM,CAACc,MAAM,CAAC,CAAC,CAAC,EAAEhB,GAAG,CAACK,GAAG,CAAC,CAAC,EAAEJ,GAAG,CAACI,GAAG,CAAC,CAAC;MACjE,CAAC,CAAC;MACF,OAAOL,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EACR,CAAC,EAAE,CAACe,MAAM,CAAC,CAAC;AACd;AACA;AACA,SAASE,kBAAkBA,CAACC,GAAG,EAAEtB,MAAM,EAAE;EACvC,MAAMuB,MAAM,GAAGjB,MAAM,CAACc,MAAM,CAAC,CAAC,CAAC,EAAEE,GAAG,CAAC;EACrChB,MAAM,CAACC,IAAI,CAACP,MAAM,CAAC,CAACQ,OAAO,CAACC,GAAG,IAAI;IACjC,IAAIA,GAAG,KAAK,UAAU,EAAE;MACtB,MAAMe,UAAU,GAAGxB,MAAM,CAACS,GAAG,CAAC;MAC9B,MAAMgB,SAAS,GAAGF,MAAM,CAACd,GAAG,CAAC,IAAI,CAAC,CAAC;MACnCc,MAAM,CAACd,GAAG,CAAC,GAAGe,UAAU,GAAGH,kBAAkB,CAACI,SAAS,EAAED,UAAU,CAAC,GAAGC,SAAS;IAClF;EACF,CAAC,CAAC;EACF,OAAOF,MAAM;AACf;AACA;AACA;AACA;AACA;AACA,eAAe,SAASG,gBAAgBA,CAACC,cAAc,EAAEC,UAAU,EAAE5B,MAAM,EAAE;EAC3E,MAAM6B,gBAAgB,GAAGf,qBAAqB,CAACE,KAAK,CAAC,KAAK,CAAC,EAAE,CAAChB,MAAM,CAAC,CAACiB,MAAM,CAACrB,kBAAkB,CAAC+B,cAAc,CAAC,CAAC,CAAC;EACjH,MAAMG,YAAY,GAAGZ,iBAAiB,CAACF,KAAK,CAAC,KAAK,CAAC,EAAEpB,kBAAkB,CAACgC,UAAU,CAAC,CAAC;EACpF,OAAO/B,KAAK,CAACkB,OAAO,CAAC,MAAM;IACzB,OAAO,CAACM,kBAAkB,CAACQ,gBAAgB,EAAE7B,MAAM,CAAC,EAAEqB,kBAAkB,CAACS,YAAY,EAAE9B,MAAM,CAAC,CAAC;EACjG,CAAC,EAAE,CAAC6B,gBAAgB,EAAEC,YAAY,CAAC,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}