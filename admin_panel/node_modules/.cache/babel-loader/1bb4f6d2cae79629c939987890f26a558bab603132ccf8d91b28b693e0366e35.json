{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport TagsFilledSvg from \"@ant-design/icons-svg/es/asn/TagsFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar TagsFilled = function TagsFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: TagsFilledSvg\n  }));\n};\n\n/**![tags](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ4My4yIDc5MC4zTDg2MS40IDQxMmMxLjctMS43IDIuNS00IDIuMy02LjNsLTI1LjUtMzAxLjRjLS43LTcuOC02LjgtMTMuOS0xNC42LTE0LjZMNTIyLjIgNjQuM2MtMi4zLS4yLTQuNy42LTYuMyAyLjNMMTM3LjcgNDQ0LjhhOC4wMyA4LjAzIDAgMDAwIDExLjNsMzM0LjIgMzM0LjJjMy4xIDMuMiA4LjIgMy4yIDExLjMgMHptMTIyLjctNTMzLjRjMTguNy0xOC43IDQ5LjEtMTguNyA2Ny45IDAgMTguNyAxOC43IDE4LjcgNDkuMSAwIDY3LjktMTguNyAxOC43LTQ5LjEgMTguNy02Ny45IDAtMTguNy0xOC43LTE4LjctNDkuMSAwLTY3Ljl6bTI4My44IDI4Mi45bC0zOS42LTM5LjVhOC4wMyA4LjAzIDAgMDAtMTEuMyAwbC0zNjIgMzYxLjMtMjM3LjYtMjM3YTguMDMgOC4wMyAwIDAwLTExLjMgMGwtMzkuNiAzOS41YTguMDMgOC4wMyAwIDAwMCAxMS4zbDI0My4yIDI0Mi44IDM5LjYgMzkuNWMzLjEgMy4xIDguMiAzLjEgMTEuMyAwbDQwNy4zLTQwNi42YzMuMS0zLjEgMy4xLTguMiAwLTExLjN6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(TagsFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'TagsFilled';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "TagsFilledSvg", "AntdIcon", "TagsFilled", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/antd/node_modules/@ant-design/icons/es/icons/TagsFilled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport TagsFilledSvg from \"@ant-design/icons-svg/es/asn/TagsFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar TagsFilled = function TagsFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: TagsFilledSvg\n  }));\n};\n\n/**![tags](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ4My4yIDc5MC4zTDg2MS40IDQxMmMxLjctMS43IDIuNS00IDIuMy02LjNsLTI1LjUtMzAxLjRjLS43LTcuOC02LjgtMTMuOS0xNC42LTE0LjZMNTIyLjIgNjQuM2MtMi4zLS4yLTQuNy42LTYuMyAyLjNMMTM3LjcgNDQ0LjhhOC4wMyA4LjAzIDAgMDAwIDExLjNsMzM0LjIgMzM0LjJjMy4xIDMuMiA4LjIgMy4yIDExLjMgMHptMTIyLjctNTMzLjRjMTguNy0xOC43IDQ5LjEtMTguNyA2Ny45IDAgMTguNyAxOC43IDE4LjcgNDkuMSAwIDY3LjktMTguNyAxOC43LTQ5LjEgMTguNy02Ny45IDAtMTguNy0xOC43LTE4LjctNDkuMSAwLTY3Ljl6bTI4My44IDI4Mi45bC0zOS42LTM5LjVhOC4wMyA4LjAzIDAgMDAtMTEuMyAwbC0zNjIgMzYxLjMtMjM3LjYtMjM3YTguMDMgOC4wMyAwIDAwLTExLjMgMGwtMzkuNiAzOS41YTguMDMgOC4wMyAwIDAwMCAxMS4zbDI0My4yIDI0Mi44IDM5LjYgMzkuNWMzLjEgMy4xIDguMiAzLjEgMTEuMyAwbDQwNy4zLTQwNi42YzMuMS0zLjEgMy4xLTguMiAwLTExLjN6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(TagsFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'TagsFilled';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,yCAAyC;AACnE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC/C,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,UAAU,CAAC;AACvD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,YAAY;AACpC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}