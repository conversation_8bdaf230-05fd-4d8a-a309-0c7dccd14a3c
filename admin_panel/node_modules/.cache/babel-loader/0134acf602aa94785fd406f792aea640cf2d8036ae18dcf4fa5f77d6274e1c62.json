{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genBorderedStyle = token => {\n  const {\n    componentCls,\n    lineWidth,\n    lineType,\n    tableBorderColor,\n    tableHeaderBg,\n    tablePaddingVertical,\n    tablePaddingHorizontal,\n    calc\n  } = token;\n  const tableBorder = `${unit(lineWidth)} ${lineType} ${tableBorderColor}`;\n  const getSizeBorderStyle = (size, paddingVertical, paddingHorizontal) => ({\n    [`&${componentCls}-${size}`]: {\n      [`> ${componentCls}-container`]: {\n        [`> ${componentCls}-content, > ${componentCls}-body`]: {\n          [`\n            > table > tbody > tr > th,\n            > table > tbody > tr > td\n          `]: {\n            [`> ${componentCls}-expanded-row-fixed`]: {\n              margin: `${unit(calc(paddingVertical).mul(-1).equal())}\n              ${unit(calc(calc(paddingHorizontal).add(lineWidth)).mul(-1).equal())}`\n            }\n          }\n        }\n      }\n    }\n  });\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}${componentCls}-bordered`]: Object.assign(Object.assign(Object.assign({\n        // ============================ Title =============================\n        [`> ${componentCls}-title`]: {\n          border: tableBorder,\n          borderBottom: 0\n        },\n        // ============================ Content ============================\n        [`> ${componentCls}-container`]: {\n          borderInlineStart: tableBorder,\n          borderTop: tableBorder,\n          [`\n            > ${componentCls}-content,\n            > ${componentCls}-header,\n            > ${componentCls}-body,\n            > ${componentCls}-summary\n          `]: {\n            '> table': {\n              // ============================= Cell =============================\n              [`\n                > thead > tr > th,\n                > thead > tr > td,\n                > tbody > tr > th,\n                > tbody > tr > td,\n                > tfoot > tr > th,\n                > tfoot > tr > td\n              `]: {\n                borderInlineEnd: tableBorder\n              },\n              // ============================ Header ============================\n              '> thead': {\n                '> tr:not(:last-child) > th': {\n                  borderBottom: tableBorder\n                },\n                '> tr > th::before': {\n                  backgroundColor: 'transparent !important'\n                }\n              },\n              // Fixed right should provides additional border\n              [`\n                > thead > tr,\n                > tbody > tr,\n                > tfoot > tr\n              `]: {\n                [`> ${componentCls}-cell-fix-right-first::after`]: {\n                  borderInlineEnd: tableBorder\n                }\n              },\n              // ========================== Expandable ==========================\n              [`\n                > tbody > tr > th,\n                > tbody > tr > td\n              `]: {\n                [`> ${componentCls}-expanded-row-fixed`]: {\n                  margin: `${unit(calc(tablePaddingVertical).mul(-1).equal())} ${unit(calc(calc(tablePaddingHorizontal).add(lineWidth)).mul(-1).equal())}`,\n                  '&::after': {\n                    position: 'absolute',\n                    top: 0,\n                    insetInlineEnd: lineWidth,\n                    bottom: 0,\n                    borderInlineEnd: tableBorder,\n                    content: '\"\"'\n                  }\n                }\n              }\n            }\n          }\n        },\n        // ============================ Scroll ============================\n        [`&${componentCls}-scroll-horizontal`]: {\n          [`> ${componentCls}-container > ${componentCls}-body`]: {\n            '> table > tbody': {\n              [`\n                > tr${componentCls}-expanded-row,\n                > tr${componentCls}-placeholder\n              `]: {\n                '> th, > td': {\n                  borderInlineEnd: 0\n                }\n              }\n            }\n          }\n        }\n      }, getSizeBorderStyle('middle', token.tablePaddingVerticalMiddle, token.tablePaddingHorizontalMiddle)), getSizeBorderStyle('small', token.tablePaddingVerticalSmall, token.tablePaddingHorizontalSmall)), {\n        // ============================ Footer ============================\n        [`> ${componentCls}-footer`]: {\n          border: tableBorder,\n          borderTop: 0\n        }\n      }),\n      // ============================ Nested ============================\n      [`${componentCls}-cell`]: {\n        [`${componentCls}-container:first-child`]: {\n          // :first-child to avoid the case when bordered and title is set\n          borderTop: 0\n        },\n        // https://github.com/ant-design/ant-design/issues/35577\n        '&-scrollbar:not([rowspan])': {\n          boxShadow: `0 ${unit(lineWidth)} 0 ${unit(lineWidth)} ${tableHeaderBg}`\n        }\n      },\n      [`${componentCls}-bordered ${componentCls}-cell-scrollbar`]: {\n        borderInlineEnd: tableBorder\n      }\n    }\n  };\n};\nexport default genBorderedStyle;", "map": {"version": 3, "names": ["unit", "genBorderedStyle", "token", "componentCls", "lineWidth", "lineType", "tableBorderColor", "tableHeaderBg", "tablePaddingVertical", "tablePaddingHorizontal", "calc", "tableBorder", "getSizeBorderStyle", "size", "paddingVertical", "paddingHorizontal", "margin", "mul", "equal", "add", "Object", "assign", "border", "borderBottom", "borderInlineStart", "borderTop", "borderInlineEnd", "backgroundColor", "position", "top", "insetInlineEnd", "bottom", "content", "tablePaddingVerticalMiddle", "tablePaddingHorizontalMiddle", "tablePaddingVerticalSmall", "tablePaddingHorizontalSmall", "boxShadow"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/antd/es/table/style/bordered.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst genBorderedStyle = token => {\n  const {\n    componentCls,\n    lineWidth,\n    lineType,\n    tableBorderColor,\n    tableHeaderBg,\n    tablePaddingVertical,\n    tablePaddingHorizontal,\n    calc\n  } = token;\n  const tableBorder = `${unit(lineWidth)} ${lineType} ${tableBorderColor}`;\n  const getSizeBorderStyle = (size, paddingVertical, paddingHorizontal) => ({\n    [`&${componentCls}-${size}`]: {\n      [`> ${componentCls}-container`]: {\n        [`> ${componentCls}-content, > ${componentCls}-body`]: {\n          [`\n            > table > tbody > tr > th,\n            > table > tbody > tr > td\n          `]: {\n            [`> ${componentCls}-expanded-row-fixed`]: {\n              margin: `${unit(calc(paddingVertical).mul(-1).equal())}\n              ${unit(calc(calc(paddingHorizontal).add(lineWidth)).mul(-1).equal())}`\n            }\n          }\n        }\n      }\n    }\n  });\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}${componentCls}-bordered`]: Object.assign(Object.assign(Object.assign({\n        // ============================ Title =============================\n        [`> ${componentCls}-title`]: {\n          border: tableBorder,\n          borderBottom: 0\n        },\n        // ============================ Content ============================\n        [`> ${componentCls}-container`]: {\n          borderInlineStart: tableBorder,\n          borderTop: tableBorder,\n          [`\n            > ${componentCls}-content,\n            > ${componentCls}-header,\n            > ${componentCls}-body,\n            > ${componentCls}-summary\n          `]: {\n            '> table': {\n              // ============================= Cell =============================\n              [`\n                > thead > tr > th,\n                > thead > tr > td,\n                > tbody > tr > th,\n                > tbody > tr > td,\n                > tfoot > tr > th,\n                > tfoot > tr > td\n              `]: {\n                borderInlineEnd: tableBorder\n              },\n              // ============================ Header ============================\n              '> thead': {\n                '> tr:not(:last-child) > th': {\n                  borderBottom: tableBorder\n                },\n                '> tr > th::before': {\n                  backgroundColor: 'transparent !important'\n                }\n              },\n              // Fixed right should provides additional border\n              [`\n                > thead > tr,\n                > tbody > tr,\n                > tfoot > tr\n              `]: {\n                [`> ${componentCls}-cell-fix-right-first::after`]: {\n                  borderInlineEnd: tableBorder\n                }\n              },\n              // ========================== Expandable ==========================\n              [`\n                > tbody > tr > th,\n                > tbody > tr > td\n              `]: {\n                [`> ${componentCls}-expanded-row-fixed`]: {\n                  margin: `${unit(calc(tablePaddingVertical).mul(-1).equal())} ${unit(calc(calc(tablePaddingHorizontal).add(lineWidth)).mul(-1).equal())}`,\n                  '&::after': {\n                    position: 'absolute',\n                    top: 0,\n                    insetInlineEnd: lineWidth,\n                    bottom: 0,\n                    borderInlineEnd: tableBorder,\n                    content: '\"\"'\n                  }\n                }\n              }\n            }\n          }\n        },\n        // ============================ Scroll ============================\n        [`&${componentCls}-scroll-horizontal`]: {\n          [`> ${componentCls}-container > ${componentCls}-body`]: {\n            '> table > tbody': {\n              [`\n                > tr${componentCls}-expanded-row,\n                > tr${componentCls}-placeholder\n              `]: {\n                '> th, > td': {\n                  borderInlineEnd: 0\n                }\n              }\n            }\n          }\n        }\n      }, getSizeBorderStyle('middle', token.tablePaddingVerticalMiddle, token.tablePaddingHorizontalMiddle)), getSizeBorderStyle('small', token.tablePaddingVerticalSmall, token.tablePaddingHorizontalSmall)), {\n        // ============================ Footer ============================\n        [`> ${componentCls}-footer`]: {\n          border: tableBorder,\n          borderTop: 0\n        }\n      }),\n      // ============================ Nested ============================\n      [`${componentCls}-cell`]: {\n        [`${componentCls}-container:first-child`]: {\n          // :first-child to avoid the case when bordered and title is set\n          borderTop: 0\n        },\n        // https://github.com/ant-design/ant-design/issues/35577\n        '&-scrollbar:not([rowspan])': {\n          boxShadow: `0 ${unit(lineWidth)} 0 ${unit(lineWidth)} ${tableHeaderBg}`\n        }\n      },\n      [`${componentCls}-bordered ${componentCls}-cell-scrollbar`]: {\n        borderInlineEnd: tableBorder\n      }\n    }\n  };\n};\nexport default genBorderedStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,MAAMC,gBAAgB,GAAGC,KAAK,IAAI;EAChC,MAAM;IACJC,YAAY;IACZC,SAAS;IACTC,QAAQ;IACRC,gBAAgB;IAChBC,aAAa;IACbC,oBAAoB;IACpBC,sBAAsB;IACtBC;EACF,CAAC,GAAGR,KAAK;EACT,MAAMS,WAAW,GAAG,GAAGX,IAAI,CAACI,SAAS,CAAC,IAAIC,QAAQ,IAAIC,gBAAgB,EAAE;EACxE,MAAMM,kBAAkB,GAAGA,CAACC,IAAI,EAAEC,eAAe,EAAEC,iBAAiB,MAAM;IACxE,CAAC,IAAIZ,YAAY,IAAIU,IAAI,EAAE,GAAG;MAC5B,CAAC,KAAKV,YAAY,YAAY,GAAG;QAC/B,CAAC,KAAKA,YAAY,eAAeA,YAAY,OAAO,GAAG;UACrD,CAAC;AACX;AACA;AACA,WAAW,GAAG;YACF,CAAC,KAAKA,YAAY,qBAAqB,GAAG;cACxCa,MAAM,EAAE,GAAGhB,IAAI,CAACU,IAAI,CAACI,eAAe,CAAC,CAACG,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;AACpE,gBAAgBlB,IAAI,CAACU,IAAI,CAACA,IAAI,CAACK,iBAAiB,CAAC,CAACI,GAAG,CAACf,SAAS,CAAC,CAAC,CAACa,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;YACtE;UACF;QACF;MACF;IACF;EACF,CAAC,CAAC;EACF,OAAO;IACL,CAAC,GAAGf,YAAY,UAAU,GAAG;MAC3B,CAAC,GAAGA,YAAY,GAAGA,YAAY,WAAW,GAAGiB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QACrF;QACA,CAAC,KAAKlB,YAAY,QAAQ,GAAG;UAC3BmB,MAAM,EAAEX,WAAW;UACnBY,YAAY,EAAE;QAChB,CAAC;QACD;QACA,CAAC,KAAKpB,YAAY,YAAY,GAAG;UAC/BqB,iBAAiB,EAAEb,WAAW;UAC9Bc,SAAS,EAAEd,WAAW;UACtB,CAAC;AACX,gBAAgBR,YAAY;AAC5B,gBAAgBA,YAAY;AAC5B,gBAAgBA,YAAY;AAC5B,gBAAgBA,YAAY;AAC5B,WAAW,GAAG;YACF,SAAS,EAAE;cACT;cACA,CAAC;AACf;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,GAAG;gBACFuB,eAAe,EAAEf;cACnB,CAAC;cACD;cACA,SAAS,EAAE;gBACT,4BAA4B,EAAE;kBAC5BY,YAAY,EAAEZ;gBAChB,CAAC;gBACD,mBAAmB,EAAE;kBACnBgB,eAAe,EAAE;gBACnB;cACF,CAAC;cACD;cACA,CAAC;AACf;AACA;AACA;AACA,eAAe,GAAG;gBACF,CAAC,KAAKxB,YAAY,8BAA8B,GAAG;kBACjDuB,eAAe,EAAEf;gBACnB;cACF,CAAC;cACD;cACA,CAAC;AACf;AACA;AACA,eAAe,GAAG;gBACF,CAAC,KAAKR,YAAY,qBAAqB,GAAG;kBACxCa,MAAM,EAAE,GAAGhB,IAAI,CAACU,IAAI,CAACF,oBAAoB,CAAC,CAACS,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,IAAIlB,IAAI,CAACU,IAAI,CAACA,IAAI,CAACD,sBAAsB,CAAC,CAACU,GAAG,CAACf,SAAS,CAAC,CAAC,CAACa,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE;kBACxI,UAAU,EAAE;oBACVU,QAAQ,EAAE,UAAU;oBACpBC,GAAG,EAAE,CAAC;oBACNC,cAAc,EAAE1B,SAAS;oBACzB2B,MAAM,EAAE,CAAC;oBACTL,eAAe,EAAEf,WAAW;oBAC5BqB,OAAO,EAAE;kBACX;gBACF;cACF;YACF;UACF;QACF,CAAC;QACD;QACA,CAAC,IAAI7B,YAAY,oBAAoB,GAAG;UACtC,CAAC,KAAKA,YAAY,gBAAgBA,YAAY,OAAO,GAAG;YACtD,iBAAiB,EAAE;cACjB,CAAC;AACf,sBAAsBA,YAAY;AAClC,sBAAsBA,YAAY;AAClC,eAAe,GAAG;gBACF,YAAY,EAAE;kBACZuB,eAAe,EAAE;gBACnB;cACF;YACF;UACF;QACF;MACF,CAAC,EAAEd,kBAAkB,CAAC,QAAQ,EAAEV,KAAK,CAAC+B,0BAA0B,EAAE/B,KAAK,CAACgC,4BAA4B,CAAC,CAAC,EAAEtB,kBAAkB,CAAC,OAAO,EAAEV,KAAK,CAACiC,yBAAyB,EAAEjC,KAAK,CAACkC,2BAA2B,CAAC,CAAC,EAAE;QACxM;QACA,CAAC,KAAKjC,YAAY,SAAS,GAAG;UAC5BmB,MAAM,EAAEX,WAAW;UACnBc,SAAS,EAAE;QACb;MACF,CAAC,CAAC;MACF;MACA,CAAC,GAAGtB,YAAY,OAAO,GAAG;QACxB,CAAC,GAAGA,YAAY,wBAAwB,GAAG;UACzC;UACAsB,SAAS,EAAE;QACb,CAAC;QACD;QACA,4BAA4B,EAAE;UAC5BY,SAAS,EAAE,KAAKrC,IAAI,CAACI,SAAS,CAAC,MAAMJ,IAAI,CAACI,SAAS,CAAC,IAAIG,aAAa;QACvE;MACF,CAAC;MACD,CAAC,GAAGJ,YAAY,aAAaA,YAAY,iBAAiB,GAAG;QAC3DuB,eAAe,EAAEf;MACnB;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeV,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}