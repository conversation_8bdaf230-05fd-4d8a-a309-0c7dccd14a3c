{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classnames from 'classnames';\nimport React from 'react';\nvar PanelContent = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    forceRender = props.forceRender,\n    className = props.className,\n    style = props.style,\n    children = props.children,\n    isActive = props.isActive,\n    role = props.role,\n    customizeClassNames = props.classNames,\n    styles = props.styles;\n  var _React$useState = React.useState(isActive || forceRender),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    rendered = _React$useState2[0],\n    setRendered = _React$useState2[1];\n  React.useEffect(function () {\n    if (forceRender || isActive) {\n      setRendered(true);\n    }\n  }, [forceRender, isActive]);\n  if (!rendered) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: ref,\n    className: classnames(\"\".concat(prefixCls, \"-content\"), _defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-content-active\"), isActive), \"\".concat(prefixCls, \"-content-inactive\"), !isActive), className),\n    style: style,\n    role: role\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classnames(\"\".concat(prefixCls, \"-content-box\"), customizeClassNames === null || customizeClassNames === void 0 ? void 0 : customizeClassNames.body),\n    style: styles === null || styles === void 0 ? void 0 : styles.body\n  }, children));\n});\nPanelContent.displayName = 'PanelContent';\nexport default PanelContent;", "map": {"version": 3, "names": ["_defineProperty", "_slicedToArray", "classnames", "React", "PanelContent", "forwardRef", "props", "ref", "prefixCls", "forceRender", "className", "style", "children", "isActive", "role", "customizeClassNames", "classNames", "styles", "_React$useState", "useState", "_React$useState2", "rendered", "setRendered", "useEffect", "createElement", "concat", "body", "displayName"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/rc-collapse/es/PanelContent.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classnames from 'classnames';\nimport React from 'react';\nvar PanelContent = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    forceRender = props.forceRender,\n    className = props.className,\n    style = props.style,\n    children = props.children,\n    isActive = props.isActive,\n    role = props.role,\n    customizeClassNames = props.classNames,\n    styles = props.styles;\n  var _React$useState = React.useState(isActive || forceRender),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    rendered = _React$useState2[0],\n    setRendered = _React$useState2[1];\n  React.useEffect(function () {\n    if (forceRender || isActive) {\n      setRendered(true);\n    }\n  }, [forceRender, isActive]);\n  if (!rendered) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: ref,\n    className: classnames(\"\".concat(prefixCls, \"-content\"), _defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-content-active\"), isActive), \"\".concat(prefixCls, \"-content-inactive\"), !isActive), className),\n    style: style,\n    role: role\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classnames(\"\".concat(prefixCls, \"-content-box\"), customizeClassNames === null || customizeClassNames === void 0 ? void 0 : customizeClassNames.body),\n    style: styles === null || styles === void 0 ? void 0 : styles.body\n  }, children));\n});\nPanelContent.displayName = 'PanelContent';\nexport default PanelContent;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,MAAM,OAAO;AACzB,IAAIC,YAAY,GAAG,aAAaD,KAAK,CAACE,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACrE,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BC,WAAW,GAAGH,KAAK,CAACG,WAAW;IAC/BC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,QAAQ,GAAGP,KAAK,CAACO,QAAQ;IACzBC,IAAI,GAAGR,KAAK,CAACQ,IAAI;IACjBC,mBAAmB,GAAGT,KAAK,CAACU,UAAU;IACtCC,MAAM,GAAGX,KAAK,CAACW,MAAM;EACvB,IAAIC,eAAe,GAAGf,KAAK,CAACgB,QAAQ,CAACN,QAAQ,IAAIJ,WAAW,CAAC;IAC3DW,gBAAgB,GAAGnB,cAAc,CAACiB,eAAe,EAAE,CAAC,CAAC;IACrDG,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC9BE,WAAW,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACnCjB,KAAK,CAACoB,SAAS,CAAC,YAAY;IAC1B,IAAId,WAAW,IAAII,QAAQ,EAAE;MAC3BS,WAAW,CAAC,IAAI,CAAC;IACnB;EACF,CAAC,EAAE,CAACb,WAAW,EAAEI,QAAQ,CAAC,CAAC;EAC3B,IAAI,CAACQ,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;EACA,OAAO,aAAalB,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;IAC7CjB,GAAG,EAAEA,GAAG;IACRG,SAAS,EAAER,UAAU,CAAC,EAAE,CAACuB,MAAM,CAACjB,SAAS,EAAE,UAAU,CAAC,EAAER,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACyB,MAAM,CAACjB,SAAS,EAAE,iBAAiB,CAAC,EAAEK,QAAQ,CAAC,EAAE,EAAE,CAACY,MAAM,CAACjB,SAAS,EAAE,mBAAmB,CAAC,EAAE,CAACK,QAAQ,CAAC,EAAEH,SAAS,CAAC;IACjNC,KAAK,EAAEA,KAAK;IACZG,IAAI,EAAEA;EACR,CAAC,EAAE,aAAaX,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;IACzCd,SAAS,EAAER,UAAU,CAAC,EAAE,CAACuB,MAAM,CAACjB,SAAS,EAAE,cAAc,CAAC,EAAEO,mBAAmB,KAAK,IAAI,IAAIA,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACW,IAAI,CAAC;IAC/Jf,KAAK,EAAEM,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACS;EAChE,CAAC,EAAEd,QAAQ,CAAC,CAAC;AACf,CAAC,CAAC;AACFR,YAAY,CAACuB,WAAW,GAAG,cAAc;AACzC,eAAevB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}