{"ast": null, "code": "export function nextSlice(callback) {\n  /* istanbul ignore next */\n  Promise.resolve().then(callback);\n}", "map": {"version": 3, "names": ["nextSlice", "callback", "Promise", "resolve", "then"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/rc-menu/es/utils/timeUtil.js"], "sourcesContent": ["export function nextSlice(callback) {\n  /* istanbul ignore next */\n  Promise.resolve().then(callback);\n}"], "mappings": "AAAA,OAAO,SAASA,SAASA,CAACC,QAAQ,EAAE;EAClC;EACAC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAACH,QAAQ,CAAC;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}