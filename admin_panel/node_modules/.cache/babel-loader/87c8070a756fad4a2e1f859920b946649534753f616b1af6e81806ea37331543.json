{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport QuestionCircleFilledSvg from \"@ant-design/icons-svg/es/asn/QuestionCircleFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar QuestionCircleFilled = function QuestionCircleFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: QuestionCircleFilledSvg\n  }));\n};\n\n/**![question-circle](data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(QuestionCircleFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'QuestionCircleFilled';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "QuestionCircleFilledSvg", "AntdIcon", "QuestionCircleFilled", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/antd/node_modules/@ant-design/icons/es/icons/QuestionCircleFilled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport QuestionCircleFilledSvg from \"@ant-design/icons-svg/es/asn/QuestionCircleFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar QuestionCircleFilled = function QuestionCircleFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: QuestionCircleFilledSvg\n  }));\n};\n\n/**![question-circle](data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(QuestionCircleFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'QuestionCircleFilled';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,uBAAuB,MAAM,mDAAmD;AACvF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACnE,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,oBAAoB,CAAC;AACjE,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,sBAAsB;AAC9C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}