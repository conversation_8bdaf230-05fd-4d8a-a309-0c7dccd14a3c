{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SketchSquareFilledSvg from \"@ant-design/icons-svg/es/asn/SketchSquareFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SketchSquareFilled = function SketchSquareFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SketchSquareFilledSvg\n  }));\n};\n\n/**![sketch-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTYwOC4yIDQyMy4zTDUxMiAzMjYuMWwtOTYuMiA5Ny4yem0tMjUuOSAyMDIuM2wxNDcuOS0xNjYuM2gtNjMuNHptOTAtMjAyLjNoNjIuNWwtOTIuMS0xMTUuMXpNODgwIDExMkgxNDRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjczNmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg3MzZjMTcuNyAwIDMyLTE0LjMgMzItMzJWMTQ0YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tODEuMyAzMzIuMkw1MTUuOCA3NjIuM2MtMSAxLjEtMi40IDEuNy0zLjggMS43cy0yLjgtLjYtMy44LTEuN0wyMjUuMyA0NDQuMmE1LjE0IDUuMTQgMCAwMS0uMi02LjZMMzY1LjYgMjYyYzEtMS4yIDIuNC0xLjkgNC0xLjloMjg0LjZjMS42IDAgMyAuNyA0IDEuOWwxNDAuNSAxNzUuNmE0LjkgNC45IDAgMDEwIDYuNnptLTQwMS4xIDE1LjFMNTEyIDY4NC41bDExNC40LTIyNS4yem0tMTYuMy0xNTEuMWwtOTIuMSAxMTUuMWg2Mi41em0tODcuNSAxNTEuMWwxNDcuOSAxNjYuMy04NC41LTE2Ni4zem0xMjYuNS0xNTguMmwtMjMuMSA4OS44IDg4LjgtODkuOHptMTgzLjQgMEg1MzhsODguOCA4OS44eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SketchSquareFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SketchSquareFilled';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "SketchSquareFilledSvg", "AntdIcon", "SketchSquareFilled", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/antd/node_modules/@ant-design/icons/es/icons/SketchSquareFilled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SketchSquareFilledSvg from \"@ant-design/icons-svg/es/asn/SketchSquareFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SketchSquareFilled = function SketchSquareFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SketchSquareFilledSvg\n  }));\n};\n\n/**![sketch-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTYwOC4yIDQyMy4zTDUxMiAzMjYuMWwtOTYuMiA5Ny4yem0tMjUuOSAyMDIuM2wxNDcuOS0xNjYuM2gtNjMuNHptOTAtMjAyLjNoNjIuNWwtOTIuMS0xMTUuMXpNODgwIDExMkgxNDRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjczNmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg3MzZjMTcuNyAwIDMyLTE0LjMgMzItMzJWMTQ0YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tODEuMyAzMzIuMkw1MTUuOCA3NjIuM2MtMSAxLjEtMi40IDEuNy0zLjggMS43cy0yLjgtLjYtMy44LTEuN0wyMjUuMyA0NDQuMmE1LjE0IDUuMTQgMCAwMS0uMi02LjZMMzY1LjYgMjYyYzEtMS4yIDIuNC0xLjkgNC0xLjloMjg0LjZjMS42IDAgMyAuNyA0IDEuOWwxNDAuNSAxNzUuNmE0LjkgNC45IDAgMDEwIDYuNnptLTQwMS4xIDE1LjFMNTEyIDY4NC41bDExNC40LTIyNS4yem0tMTYuMy0xNTEuMWwtOTIuMSAxMTUuMWg2Mi41em0tODcuNSAxNTEuMWwxNDcuOSAxNjYuMy04NC41LTE2Ni4zem0xMjYuNS0xNTguMmwtMjMuMSA4OS44IDg4LjgtODkuOHptMTgzLjQgMEg1MzhsODguOCA4OS44eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SketchSquareFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SketchSquareFilled';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,qBAAqB,MAAM,iDAAiD;AACnF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC/D,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,kBAAkB,CAAC;AAC/D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,oBAAoB;AAC5C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}