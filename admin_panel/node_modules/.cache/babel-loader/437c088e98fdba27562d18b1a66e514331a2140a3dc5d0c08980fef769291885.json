{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SisternodeOutlinedSvg from \"@ant-design/icons-svg/es/asn/SisternodeOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SisternodeOutlined = function SisternodeOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SisternodeOutlinedSvg\n  }));\n};\n\n/**![sisternode](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik02NzIgNDMyYy0xMjAuMyAwLTIxOS45IDg4LjUtMjM3LjMgMjA0SDMyMGMtMTUuNSAwLTI4LTEyLjUtMjgtMjhWMjQ0aDI5MWMxNC4yIDM1LjIgNDguNyA2MCA4OSA2MCA1MyAwIDk2LTQzIDk2LTk2cy00My05Ni05Ni05NmMtNDAuMyAwLTc0LjggMjQuOC04OSA2MEgxMTJ2NzJoMTA4djM2NGMwIDU1LjIgNDQuOCAxMDAgMTAwIDEwMGgxMTQuN2MxNy40IDExNS41IDExNyAyMDQgMjM3LjMgMjA0IDEzMi41IDAgMjQwLTEwNy41IDI0MC0yNDBTODA0LjUgNDMyIDY3MiA0MzJ6bTEyOCAyNjZjMCA0LjQtMy42IDgtOCA4aC04NnY4NmMwIDQuNC0zLjYgOC04IDhoLTUyYy00LjQgMC04LTMuNi04LTh2LTg2aC04NmMtNC40IDAtOC0zLjYtOC04di01MmMwLTQuNCAzLjYtOCA4LThoODZ2LTg2YzAtNC40IDMuNi04IDgtOGg1MmM0LjQgMCA4IDMuNiA4IDh2ODZoODZjNC40IDAgOCAzLjYgOCA4djUyeiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SisternodeOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SisternodeOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "SisternodeOutlinedSvg", "AntdIcon", "SisternodeOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/antd/node_modules/@ant-design/icons/es/icons/SisternodeOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SisternodeOutlinedSvg from \"@ant-design/icons-svg/es/asn/SisternodeOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SisternodeOutlined = function SisternodeOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SisternodeOutlinedSvg\n  }));\n};\n\n/**![sisternode](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik02NzIgNDMyYy0xMjAuMyAwLTIxOS45IDg4LjUtMjM3LjMgMjA0SDMyMGMtMTUuNSAwLTI4LTEyLjUtMjgtMjhWMjQ0aDI5MWMxNC4yIDM1LjIgNDguNyA2MCA4OSA2MCA1MyAwIDk2LTQzIDk2LTk2cy00My05Ni05Ni05NmMtNDAuMyAwLTc0LjggMjQuOC04OSA2MEgxMTJ2NzJoMTA4djM2NGMwIDU1LjIgNDQuOCAxMDAgMTAwIDEwMGgxMTQuN2MxNy40IDExNS41IDExNyAyMDQgMjM3LjMgMjA0IDEzMi41IDAgMjQwLTEwNy41IDI0MC0yNDBTODA0LjUgNDMyIDY3MiA0MzJ6bTEyOCAyNjZjMCA0LjQtMy42IDgtOCA4aC04NnY4NmMwIDQuNC0zLjYgOC04IDhoLTUyYy00LjQgMC04LTMuNi04LTh2LTg2aC04NmMtNC40IDAtOC0zLjYtOC04di01MmMwLTQuNCAzLjYtOCA4LThoODZ2LTg2YzAtNC40IDMuNi04IDgtOGg1MmM0LjQgMCA4IDMuNiA4IDh2ODZoODZjNC40IDAgOCAzLjYgOCA4djUyeiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SisternodeOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SisternodeOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,qBAAqB,MAAM,iDAAiD;AACnF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC/D,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,kBAAkB,CAAC;AAC/D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,oBAAoB;AAC5C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}