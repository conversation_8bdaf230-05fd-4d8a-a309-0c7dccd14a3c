{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nvar _excluded = [\"className\", \"style\", \"classNames\", \"styles\"];\nimport React, { useContext, useEffect, useRef, useState } from 'react';\nimport clsx from 'classnames';\nimport { CSSMotionList } from 'rc-motion';\nimport Notice from \"./Notice\";\nimport { NotificationContext } from \"./NotificationProvider\";\nimport useStack from \"./hooks/useStack\";\nvar NoticeList = function NoticeList(props) {\n  var configList = props.configList,\n    placement = props.placement,\n    prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    motion = props.motion,\n    onAllNoticeRemoved = props.onAllNoticeRemoved,\n    onNoticeClose = props.onNoticeClose,\n    stackConfig = props.stack;\n  var _useContext = useContext(NotificationContext),\n    ctxCls = _useContext.classNames;\n  var dictRef = useRef({});\n  var _useState = useState(null),\n    _useState2 = _slicedToArray(_useState, 2),\n    latestNotice = _useState2[0],\n    setLatestNotice = _useState2[1];\n  var _useState3 = useState([]),\n    _useState4 = _slicedToArray(_useState3, 2),\n    hoverKeys = _useState4[0],\n    setHoverKeys = _useState4[1];\n  var keys = configList.map(function (config) {\n    return {\n      config: config,\n      key: String(config.key)\n    };\n  });\n  var _useStack = useStack(stackConfig),\n    _useStack2 = _slicedToArray(_useStack, 2),\n    stack = _useStack2[0],\n    _useStack2$ = _useStack2[1],\n    offset = _useStack2$.offset,\n    threshold = _useStack2$.threshold,\n    gap = _useStack2$.gap;\n  var expanded = stack && (hoverKeys.length > 0 || keys.length <= threshold);\n  var placementMotion = typeof motion === 'function' ? motion(placement) : motion;\n\n  // Clean hover key\n  useEffect(function () {\n    if (stack && hoverKeys.length > 1) {\n      setHoverKeys(function (prev) {\n        return prev.filter(function (key) {\n          return keys.some(function (_ref) {\n            var dataKey = _ref.key;\n            return key === dataKey;\n          });\n        });\n      });\n    }\n  }, [hoverKeys, keys, stack]);\n\n  // Force update latest notice\n  useEffect(function () {\n    var _keys;\n    if (stack && dictRef.current[(_keys = keys[keys.length - 1]) === null || _keys === void 0 ? void 0 : _keys.key]) {\n      var _keys2;\n      setLatestNotice(dictRef.current[(_keys2 = keys[keys.length - 1]) === null || _keys2 === void 0 ? void 0 : _keys2.key]);\n    }\n  }, [keys, stack]);\n  return /*#__PURE__*/React.createElement(CSSMotionList, _extends({\n    key: placement,\n    className: clsx(prefixCls, \"\".concat(prefixCls, \"-\").concat(placement), ctxCls === null || ctxCls === void 0 ? void 0 : ctxCls.list, className, _defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-stack\"), !!stack), \"\".concat(prefixCls, \"-stack-expanded\"), expanded)),\n    style: style,\n    keys: keys,\n    motionAppear: true\n  }, placementMotion, {\n    onAllRemoved: function onAllRemoved() {\n      onAllNoticeRemoved(placement);\n    }\n  }), function (_ref2, nodeRef) {\n    var config = _ref2.config,\n      motionClassName = _ref2.className,\n      motionStyle = _ref2.style,\n      motionIndex = _ref2.index;\n    var _ref3 = config,\n      key = _ref3.key,\n      times = _ref3.times;\n    var strKey = String(key);\n    var _ref4 = config,\n      configClassName = _ref4.className,\n      configStyle = _ref4.style,\n      configClassNames = _ref4.classNames,\n      configStyles = _ref4.styles,\n      restConfig = _objectWithoutProperties(_ref4, _excluded);\n    var dataIndex = keys.findIndex(function (item) {\n      return item.key === strKey;\n    });\n\n    // If dataIndex is -1, that means this notice has been removed in data, but still in dom\n    // Should minus (motionIndex - 1) to get the correct index because keys.length is not the same as dom length\n    var stackStyle = {};\n    if (stack) {\n      var index = keys.length - 1 - (dataIndex > -1 ? dataIndex : motionIndex - 1);\n      var transformX = placement === 'top' || placement === 'bottom' ? '-50%' : '0';\n      if (index > 0) {\n        var _dictRef$current$strK, _dictRef$current$strK2, _dictRef$current$strK3;\n        stackStyle.height = expanded ? (_dictRef$current$strK = dictRef.current[strKey]) === null || _dictRef$current$strK === void 0 ? void 0 : _dictRef$current$strK.offsetHeight : latestNotice === null || latestNotice === void 0 ? void 0 : latestNotice.offsetHeight;\n\n        // Transform\n        var verticalOffset = 0;\n        for (var i = 0; i < index; i++) {\n          var _dictRef$current$keys;\n          verticalOffset += ((_dictRef$current$keys = dictRef.current[keys[keys.length - 1 - i].key]) === null || _dictRef$current$keys === void 0 ? void 0 : _dictRef$current$keys.offsetHeight) + gap;\n        }\n        var transformY = (expanded ? verticalOffset : index * offset) * (placement.startsWith('top') ? 1 : -1);\n        var scaleX = !expanded && latestNotice !== null && latestNotice !== void 0 && latestNotice.offsetWidth && (_dictRef$current$strK2 = dictRef.current[strKey]) !== null && _dictRef$current$strK2 !== void 0 && _dictRef$current$strK2.offsetWidth ? ((latestNotice === null || latestNotice === void 0 ? void 0 : latestNotice.offsetWidth) - offset * 2 * (index < 3 ? index : 3)) / ((_dictRef$current$strK3 = dictRef.current[strKey]) === null || _dictRef$current$strK3 === void 0 ? void 0 : _dictRef$current$strK3.offsetWidth) : 1;\n        stackStyle.transform = \"translate3d(\".concat(transformX, \", \").concat(transformY, \"px, 0) scaleX(\").concat(scaleX, \")\");\n      } else {\n        stackStyle.transform = \"translate3d(\".concat(transformX, \", 0, 0)\");\n      }\n    }\n    return /*#__PURE__*/React.createElement(\"div\", {\n      ref: nodeRef,\n      className: clsx(\"\".concat(prefixCls, \"-notice-wrapper\"), motionClassName, configClassNames === null || configClassNames === void 0 ? void 0 : configClassNames.wrapper),\n      style: _objectSpread(_objectSpread(_objectSpread({}, motionStyle), stackStyle), configStyles === null || configStyles === void 0 ? void 0 : configStyles.wrapper),\n      onMouseEnter: function onMouseEnter() {\n        return setHoverKeys(function (prev) {\n          return prev.includes(strKey) ? prev : [].concat(_toConsumableArray(prev), [strKey]);\n        });\n      },\n      onMouseLeave: function onMouseLeave() {\n        return setHoverKeys(function (prev) {\n          return prev.filter(function (k) {\n            return k !== strKey;\n          });\n        });\n      }\n    }, /*#__PURE__*/React.createElement(Notice, _extends({}, restConfig, {\n      ref: function ref(node) {\n        if (dataIndex > -1) {\n          dictRef.current[strKey] = node;\n        } else {\n          delete dictRef.current[strKey];\n        }\n      },\n      prefixCls: prefixCls,\n      classNames: configClassNames,\n      styles: configStyles,\n      className: clsx(configClassName, ctxCls === null || ctxCls === void 0 ? void 0 : ctxCls.notice),\n      style: configStyle,\n      times: times,\n      key: key,\n      eventKey: key,\n      onNoticeClose: onNoticeClose,\n      hovering: stack && hoverKeys.length > 0\n    })));\n  });\n};\nif (process.env.NODE_ENV !== 'production') {\n  NoticeList.displayName = 'NoticeList';\n}\nexport default NoticeList;", "map": {"version": 3, "names": ["_extends", "_toConsumableArray", "_objectSpread", "_objectWithoutProperties", "_defineProperty", "_slicedToArray", "_excluded", "React", "useContext", "useEffect", "useRef", "useState", "clsx", "CSSMotionList", "Notice", "NotificationContext", "useStack", "NoticeList", "props", "configList", "placement", "prefixCls", "className", "style", "motion", "onAllNoticeRemoved", "onNoticeClose", "stackConfig", "stack", "_useContext", "ctxCls", "classNames", "dictRef", "_useState", "_useState2", "latestNotice", "setLatestNotice", "_useState3", "_useState4", "hoverKeys", "setHoverKeys", "keys", "map", "config", "key", "String", "_useStack", "_useStack2", "_useStack2$", "offset", "threshold", "gap", "expanded", "length", "placementMotion", "prev", "filter", "some", "_ref", "dataKey", "_keys", "current", "_keys2", "createElement", "concat", "list", "motionAppear", "onAllRemoved", "_ref2", "nodeRef", "motionClassName", "motionStyle", "motionIndex", "index", "_ref3", "times", "str<PERSON><PERSON>", "_ref4", "configClassName", "configStyle", "configClassNames", "configStyles", "styles", "restConfig", "dataIndex", "findIndex", "item", "stackStyle", "transformX", "_dictRef$current$strK", "_dictRef$current$strK2", "_dictRef$current$strK3", "height", "offsetHeight", "verticalOffset", "i", "_dictRef$current$keys", "transformY", "startsWith", "scaleX", "offsetWidth", "transform", "ref", "wrapper", "onMouseEnter", "includes", "onMouseLeave", "k", "node", "notice", "eventKey", "hovering", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/rc-notification/es/NoticeList.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nvar _excluded = [\"className\", \"style\", \"classNames\", \"styles\"];\nimport React, { useContext, useEffect, useRef, useState } from 'react';\nimport clsx from 'classnames';\nimport { CSSMotionList } from 'rc-motion';\nimport Notice from \"./Notice\";\nimport { NotificationContext } from \"./NotificationProvider\";\nimport useStack from \"./hooks/useStack\";\nvar NoticeList = function NoticeList(props) {\n  var configList = props.configList,\n    placement = props.placement,\n    prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    motion = props.motion,\n    onAllNoticeRemoved = props.onAllNoticeRemoved,\n    onNoticeClose = props.onNoticeClose,\n    stackConfig = props.stack;\n  var _useContext = useContext(NotificationContext),\n    ctxCls = _useContext.classNames;\n  var dictRef = useRef({});\n  var _useState = useState(null),\n    _useState2 = _slicedToArray(_useState, 2),\n    latestNotice = _useState2[0],\n    setLatestNotice = _useState2[1];\n  var _useState3 = useState([]),\n    _useState4 = _slicedToArray(_useState3, 2),\n    hoverKeys = _useState4[0],\n    setHoverKeys = _useState4[1];\n  var keys = configList.map(function (config) {\n    return {\n      config: config,\n      key: String(config.key)\n    };\n  });\n  var _useStack = useStack(stackConfig),\n    _useStack2 = _slicedToArray(_useStack, 2),\n    stack = _useStack2[0],\n    _useStack2$ = _useStack2[1],\n    offset = _useStack2$.offset,\n    threshold = _useStack2$.threshold,\n    gap = _useStack2$.gap;\n  var expanded = stack && (hoverKeys.length > 0 || keys.length <= threshold);\n  var placementMotion = typeof motion === 'function' ? motion(placement) : motion;\n\n  // Clean hover key\n  useEffect(function () {\n    if (stack && hoverKeys.length > 1) {\n      setHoverKeys(function (prev) {\n        return prev.filter(function (key) {\n          return keys.some(function (_ref) {\n            var dataKey = _ref.key;\n            return key === dataKey;\n          });\n        });\n      });\n    }\n  }, [hoverKeys, keys, stack]);\n\n  // Force update latest notice\n  useEffect(function () {\n    var _keys;\n    if (stack && dictRef.current[(_keys = keys[keys.length - 1]) === null || _keys === void 0 ? void 0 : _keys.key]) {\n      var _keys2;\n      setLatestNotice(dictRef.current[(_keys2 = keys[keys.length - 1]) === null || _keys2 === void 0 ? void 0 : _keys2.key]);\n    }\n  }, [keys, stack]);\n  return /*#__PURE__*/React.createElement(CSSMotionList, _extends({\n    key: placement,\n    className: clsx(prefixCls, \"\".concat(prefixCls, \"-\").concat(placement), ctxCls === null || ctxCls === void 0 ? void 0 : ctxCls.list, className, _defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-stack\"), !!stack), \"\".concat(prefixCls, \"-stack-expanded\"), expanded)),\n    style: style,\n    keys: keys,\n    motionAppear: true\n  }, placementMotion, {\n    onAllRemoved: function onAllRemoved() {\n      onAllNoticeRemoved(placement);\n    }\n  }), function (_ref2, nodeRef) {\n    var config = _ref2.config,\n      motionClassName = _ref2.className,\n      motionStyle = _ref2.style,\n      motionIndex = _ref2.index;\n    var _ref3 = config,\n      key = _ref3.key,\n      times = _ref3.times;\n    var strKey = String(key);\n    var _ref4 = config,\n      configClassName = _ref4.className,\n      configStyle = _ref4.style,\n      configClassNames = _ref4.classNames,\n      configStyles = _ref4.styles,\n      restConfig = _objectWithoutProperties(_ref4, _excluded);\n    var dataIndex = keys.findIndex(function (item) {\n      return item.key === strKey;\n    });\n\n    // If dataIndex is -1, that means this notice has been removed in data, but still in dom\n    // Should minus (motionIndex - 1) to get the correct index because keys.length is not the same as dom length\n    var stackStyle = {};\n    if (stack) {\n      var index = keys.length - 1 - (dataIndex > -1 ? dataIndex : motionIndex - 1);\n      var transformX = placement === 'top' || placement === 'bottom' ? '-50%' : '0';\n      if (index > 0) {\n        var _dictRef$current$strK, _dictRef$current$strK2, _dictRef$current$strK3;\n        stackStyle.height = expanded ? (_dictRef$current$strK = dictRef.current[strKey]) === null || _dictRef$current$strK === void 0 ? void 0 : _dictRef$current$strK.offsetHeight : latestNotice === null || latestNotice === void 0 ? void 0 : latestNotice.offsetHeight;\n\n        // Transform\n        var verticalOffset = 0;\n        for (var i = 0; i < index; i++) {\n          var _dictRef$current$keys;\n          verticalOffset += ((_dictRef$current$keys = dictRef.current[keys[keys.length - 1 - i].key]) === null || _dictRef$current$keys === void 0 ? void 0 : _dictRef$current$keys.offsetHeight) + gap;\n        }\n        var transformY = (expanded ? verticalOffset : index * offset) * (placement.startsWith('top') ? 1 : -1);\n        var scaleX = !expanded && latestNotice !== null && latestNotice !== void 0 && latestNotice.offsetWidth && (_dictRef$current$strK2 = dictRef.current[strKey]) !== null && _dictRef$current$strK2 !== void 0 && _dictRef$current$strK2.offsetWidth ? ((latestNotice === null || latestNotice === void 0 ? void 0 : latestNotice.offsetWidth) - offset * 2 * (index < 3 ? index : 3)) / ((_dictRef$current$strK3 = dictRef.current[strKey]) === null || _dictRef$current$strK3 === void 0 ? void 0 : _dictRef$current$strK3.offsetWidth) : 1;\n        stackStyle.transform = \"translate3d(\".concat(transformX, \", \").concat(transformY, \"px, 0) scaleX(\").concat(scaleX, \")\");\n      } else {\n        stackStyle.transform = \"translate3d(\".concat(transformX, \", 0, 0)\");\n      }\n    }\n    return /*#__PURE__*/React.createElement(\"div\", {\n      ref: nodeRef,\n      className: clsx(\"\".concat(prefixCls, \"-notice-wrapper\"), motionClassName, configClassNames === null || configClassNames === void 0 ? void 0 : configClassNames.wrapper),\n      style: _objectSpread(_objectSpread(_objectSpread({}, motionStyle), stackStyle), configStyles === null || configStyles === void 0 ? void 0 : configStyles.wrapper),\n      onMouseEnter: function onMouseEnter() {\n        return setHoverKeys(function (prev) {\n          return prev.includes(strKey) ? prev : [].concat(_toConsumableArray(prev), [strKey]);\n        });\n      },\n      onMouseLeave: function onMouseLeave() {\n        return setHoverKeys(function (prev) {\n          return prev.filter(function (k) {\n            return k !== strKey;\n          });\n        });\n      }\n    }, /*#__PURE__*/React.createElement(Notice, _extends({}, restConfig, {\n      ref: function ref(node) {\n        if (dataIndex > -1) {\n          dictRef.current[strKey] = node;\n        } else {\n          delete dictRef.current[strKey];\n        }\n      },\n      prefixCls: prefixCls,\n      classNames: configClassNames,\n      styles: configStyles,\n      className: clsx(configClassName, ctxCls === null || ctxCls === void 0 ? void 0 : ctxCls.notice),\n      style: configStyle,\n      times: times,\n      key: key,\n      eventKey: key,\n      onNoticeClose: onNoticeClose,\n      hovering: stack && hoverKeys.length > 0\n    })));\n  });\n};\nif (process.env.NODE_ENV !== 'production') {\n  NoticeList.displayName = 'NoticeList';\n}\nexport default NoticeList;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,CAAC;AAC9D,OAAOC,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACtE,OAAOC,IAAI,MAAM,YAAY;AAC7B,SAASC,aAAa,QAAQ,WAAW;AACzC,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,KAAK,EAAE;EAC1C,IAAIC,UAAU,GAAGD,KAAK,CAACC,UAAU;IAC/BC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,MAAM,GAAGN,KAAK,CAACM,MAAM;IACrBC,kBAAkB,GAAGP,KAAK,CAACO,kBAAkB;IAC7CC,aAAa,GAAGR,KAAK,CAACQ,aAAa;IACnCC,WAAW,GAAGT,KAAK,CAACU,KAAK;EAC3B,IAAIC,WAAW,GAAGrB,UAAU,CAACO,mBAAmB,CAAC;IAC/Ce,MAAM,GAAGD,WAAW,CAACE,UAAU;EACjC,IAAIC,OAAO,GAAGtB,MAAM,CAAC,CAAC,CAAC,CAAC;EACxB,IAAIuB,SAAS,GAAGtB,QAAQ,CAAC,IAAI,CAAC;IAC5BuB,UAAU,GAAG7B,cAAc,CAAC4B,SAAS,EAAE,CAAC,CAAC;IACzCE,YAAY,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC5BE,eAAe,GAAGF,UAAU,CAAC,CAAC,CAAC;EACjC,IAAIG,UAAU,GAAG1B,QAAQ,CAAC,EAAE,CAAC;IAC3B2B,UAAU,GAAGjC,cAAc,CAACgC,UAAU,EAAE,CAAC,CAAC;IAC1CE,SAAS,GAAGD,UAAU,CAAC,CAAC,CAAC;IACzBE,YAAY,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC9B,IAAIG,IAAI,GAAGtB,UAAU,CAACuB,GAAG,CAAC,UAAUC,MAAM,EAAE;IAC1C,OAAO;MACLA,MAAM,EAAEA,MAAM;MACdC,GAAG,EAAEC,MAAM,CAACF,MAAM,CAACC,GAAG;IACxB,CAAC;EACH,CAAC,CAAC;EACF,IAAIE,SAAS,GAAG9B,QAAQ,CAACW,WAAW,CAAC;IACnCoB,UAAU,GAAG1C,cAAc,CAACyC,SAAS,EAAE,CAAC,CAAC;IACzClB,KAAK,GAAGmB,UAAU,CAAC,CAAC,CAAC;IACrBC,WAAW,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC3BE,MAAM,GAAGD,WAAW,CAACC,MAAM;IAC3BC,SAAS,GAAGF,WAAW,CAACE,SAAS;IACjCC,GAAG,GAAGH,WAAW,CAACG,GAAG;EACvB,IAAIC,QAAQ,GAAGxB,KAAK,KAAKW,SAAS,CAACc,MAAM,GAAG,CAAC,IAAIZ,IAAI,CAACY,MAAM,IAAIH,SAAS,CAAC;EAC1E,IAAII,eAAe,GAAG,OAAO9B,MAAM,KAAK,UAAU,GAAGA,MAAM,CAACJ,SAAS,CAAC,GAAGI,MAAM;;EAE/E;EACAf,SAAS,CAAC,YAAY;IACpB,IAAImB,KAAK,IAAIW,SAAS,CAACc,MAAM,GAAG,CAAC,EAAE;MACjCb,YAAY,CAAC,UAAUe,IAAI,EAAE;QAC3B,OAAOA,IAAI,CAACC,MAAM,CAAC,UAAUZ,GAAG,EAAE;UAChC,OAAOH,IAAI,CAACgB,IAAI,CAAC,UAAUC,IAAI,EAAE;YAC/B,IAAIC,OAAO,GAAGD,IAAI,CAACd,GAAG;YACtB,OAAOA,GAAG,KAAKe,OAAO;UACxB,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACpB,SAAS,EAAEE,IAAI,EAAEb,KAAK,CAAC,CAAC;;EAE5B;EACAnB,SAAS,CAAC,YAAY;IACpB,IAAImD,KAAK;IACT,IAAIhC,KAAK,IAAII,OAAO,CAAC6B,OAAO,CAAC,CAACD,KAAK,GAAGnB,IAAI,CAACA,IAAI,CAACY,MAAM,GAAG,CAAC,CAAC,MAAM,IAAI,IAAIO,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAChB,GAAG,CAAC,EAAE;MAC/G,IAAIkB,MAAM;MACV1B,eAAe,CAACJ,OAAO,CAAC6B,OAAO,CAAC,CAACC,MAAM,GAAGrB,IAAI,CAACA,IAAI,CAACY,MAAM,GAAG,CAAC,CAAC,MAAM,IAAI,IAAIS,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAClB,GAAG,CAAC,CAAC;IACxH;EACF,CAAC,EAAE,CAACH,IAAI,EAAEb,KAAK,CAAC,CAAC;EACjB,OAAO,aAAarB,KAAK,CAACwD,aAAa,CAAClD,aAAa,EAAEb,QAAQ,CAAC;IAC9D4C,GAAG,EAAExB,SAAS;IACdE,SAAS,EAAEV,IAAI,CAACS,SAAS,EAAE,EAAE,CAAC2C,MAAM,CAAC3C,SAAS,EAAE,GAAG,CAAC,CAAC2C,MAAM,CAAC5C,SAAS,CAAC,EAAEU,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACmC,IAAI,EAAE3C,SAAS,EAAElB,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC4D,MAAM,CAAC3C,SAAS,EAAE,QAAQ,CAAC,EAAE,CAAC,CAACO,KAAK,CAAC,EAAE,EAAE,CAACoC,MAAM,CAAC3C,SAAS,EAAE,iBAAiB,CAAC,EAAE+B,QAAQ,CAAC,CAAC;IACjR7B,KAAK,EAAEA,KAAK;IACZkB,IAAI,EAAEA,IAAI;IACVyB,YAAY,EAAE;EAChB,CAAC,EAAEZ,eAAe,EAAE;IAClBa,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;MACpC1C,kBAAkB,CAACL,SAAS,CAAC;IAC/B;EACF,CAAC,CAAC,EAAE,UAAUgD,KAAK,EAAEC,OAAO,EAAE;IAC5B,IAAI1B,MAAM,GAAGyB,KAAK,CAACzB,MAAM;MACvB2B,eAAe,GAAGF,KAAK,CAAC9C,SAAS;MACjCiD,WAAW,GAAGH,KAAK,CAAC7C,KAAK;MACzBiD,WAAW,GAAGJ,KAAK,CAACK,KAAK;IAC3B,IAAIC,KAAK,GAAG/B,MAAM;MAChBC,GAAG,GAAG8B,KAAK,CAAC9B,GAAG;MACf+B,KAAK,GAAGD,KAAK,CAACC,KAAK;IACrB,IAAIC,MAAM,GAAG/B,MAAM,CAACD,GAAG,CAAC;IACxB,IAAIiC,KAAK,GAAGlC,MAAM;MAChBmC,eAAe,GAAGD,KAAK,CAACvD,SAAS;MACjCyD,WAAW,GAAGF,KAAK,CAACtD,KAAK;MACzByD,gBAAgB,GAAGH,KAAK,CAAC9C,UAAU;MACnCkD,YAAY,GAAGJ,KAAK,CAACK,MAAM;MAC3BC,UAAU,GAAGhF,wBAAwB,CAAC0E,KAAK,EAAEvE,SAAS,CAAC;IACzD,IAAI8E,SAAS,GAAG3C,IAAI,CAAC4C,SAAS,CAAC,UAAUC,IAAI,EAAE;MAC7C,OAAOA,IAAI,CAAC1C,GAAG,KAAKgC,MAAM;IAC5B,CAAC,CAAC;;IAEF;IACA;IACA,IAAIW,UAAU,GAAG,CAAC,CAAC;IACnB,IAAI3D,KAAK,EAAE;MACT,IAAI6C,KAAK,GAAGhC,IAAI,CAACY,MAAM,GAAG,CAAC,IAAI+B,SAAS,GAAG,CAAC,CAAC,GAAGA,SAAS,GAAGZ,WAAW,GAAG,CAAC,CAAC;MAC5E,IAAIgB,UAAU,GAAGpE,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,QAAQ,GAAG,MAAM,GAAG,GAAG;MAC7E,IAAIqD,KAAK,GAAG,CAAC,EAAE;QACb,IAAIgB,qBAAqB,EAAEC,sBAAsB,EAAEC,sBAAsB;QACzEJ,UAAU,CAACK,MAAM,GAAGxC,QAAQ,GAAG,CAACqC,qBAAqB,GAAGzD,OAAO,CAAC6B,OAAO,CAACe,MAAM,CAAC,MAAM,IAAI,IAAIa,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACI,YAAY,GAAG1D,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC0D,YAAY;;QAEnQ;QACA,IAAIC,cAAc,GAAG,CAAC;QACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,KAAK,EAAEsB,CAAC,EAAE,EAAE;UAC9B,IAAIC,qBAAqB;UACzBF,cAAc,IAAI,CAAC,CAACE,qBAAqB,GAAGhE,OAAO,CAAC6B,OAAO,CAACpB,IAAI,CAACA,IAAI,CAACY,MAAM,GAAG,CAAC,GAAG0C,CAAC,CAAC,CAACnD,GAAG,CAAC,MAAM,IAAI,IAAIoD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACH,YAAY,IAAI1C,GAAG;QAC/L;QACA,IAAI8C,UAAU,GAAG,CAAC7C,QAAQ,GAAG0C,cAAc,GAAGrB,KAAK,GAAGxB,MAAM,KAAK7B,SAAS,CAAC8E,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACtG,IAAIC,MAAM,GAAG,CAAC/C,QAAQ,IAAIjB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,IAAIA,YAAY,CAACiE,WAAW,IAAI,CAACV,sBAAsB,GAAG1D,OAAO,CAAC6B,OAAO,CAACe,MAAM,CAAC,MAAM,IAAI,IAAIc,sBAAsB,KAAK,KAAK,CAAC,IAAIA,sBAAsB,CAACU,WAAW,GAAG,CAAC,CAACjE,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACiE,WAAW,IAAInD,MAAM,GAAG,CAAC,IAAIwB,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,CAAC,KAAK,CAACkB,sBAAsB,GAAG3D,OAAO,CAAC6B,OAAO,CAACe,MAAM,CAAC,MAAM,IAAI,IAAIe,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACS,WAAW,CAAC,GAAG,CAAC;QACzgBb,UAAU,CAACc,SAAS,GAAG,cAAc,CAACrC,MAAM,CAACwB,UAAU,EAAE,IAAI,CAAC,CAACxB,MAAM,CAACiC,UAAU,EAAE,gBAAgB,CAAC,CAACjC,MAAM,CAACmC,MAAM,EAAE,GAAG,CAAC;MACzH,CAAC,MAAM;QACLZ,UAAU,CAACc,SAAS,GAAG,cAAc,CAACrC,MAAM,CAACwB,UAAU,EAAE,SAAS,CAAC;MACrE;IACF;IACA,OAAO,aAAajF,KAAK,CAACwD,aAAa,CAAC,KAAK,EAAE;MAC7CuC,GAAG,EAAEjC,OAAO;MACZ/C,SAAS,EAAEV,IAAI,CAAC,EAAE,CAACoD,MAAM,CAAC3C,SAAS,EAAE,iBAAiB,CAAC,EAAEiD,eAAe,EAAEU,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACuB,OAAO,CAAC;MACvKhF,KAAK,EAAErB,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqE,WAAW,CAAC,EAAEgB,UAAU,CAAC,EAAEN,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACsB,OAAO,CAAC;MACjKC,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;QACpC,OAAOhE,YAAY,CAAC,UAAUe,IAAI,EAAE;UAClC,OAAOA,IAAI,CAACkD,QAAQ,CAAC7B,MAAM,CAAC,GAAGrB,IAAI,GAAG,EAAE,CAACS,MAAM,CAAC/D,kBAAkB,CAACsD,IAAI,CAAC,EAAE,CAACqB,MAAM,CAAC,CAAC;QACrF,CAAC,CAAC;MACJ,CAAC;MACD8B,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;QACpC,OAAOlE,YAAY,CAAC,UAAUe,IAAI,EAAE;UAClC,OAAOA,IAAI,CAACC,MAAM,CAAC,UAAUmD,CAAC,EAAE;YAC9B,OAAOA,CAAC,KAAK/B,MAAM;UACrB,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC,EAAE,aAAarE,KAAK,CAACwD,aAAa,CAACjD,MAAM,EAAEd,QAAQ,CAAC,CAAC,CAAC,EAAEmF,UAAU,EAAE;MACnEmB,GAAG,EAAE,SAASA,GAAGA,CAACM,IAAI,EAAE;QACtB,IAAIxB,SAAS,GAAG,CAAC,CAAC,EAAE;UAClBpD,OAAO,CAAC6B,OAAO,CAACe,MAAM,CAAC,GAAGgC,IAAI;QAChC,CAAC,MAAM;UACL,OAAO5E,OAAO,CAAC6B,OAAO,CAACe,MAAM,CAAC;QAChC;MACF,CAAC;MACDvD,SAAS,EAAEA,SAAS;MACpBU,UAAU,EAAEiD,gBAAgB;MAC5BE,MAAM,EAAED,YAAY;MACpB3D,SAAS,EAAEV,IAAI,CAACkE,eAAe,EAAEhD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC+E,MAAM,CAAC;MAC/FtF,KAAK,EAAEwD,WAAW;MAClBJ,KAAK,EAAEA,KAAK;MACZ/B,GAAG,EAAEA,GAAG;MACRkE,QAAQ,EAAElE,GAAG;MACblB,aAAa,EAAEA,aAAa;MAC5BqF,QAAQ,EAAEnF,KAAK,IAAIW,SAAS,CAACc,MAAM,GAAG;IACxC,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,CAAC;AACJ,CAAC;AACD,IAAI2D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCjG,UAAU,CAACkG,WAAW,GAAG,YAAY;AACvC;AACA,eAAelG,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}