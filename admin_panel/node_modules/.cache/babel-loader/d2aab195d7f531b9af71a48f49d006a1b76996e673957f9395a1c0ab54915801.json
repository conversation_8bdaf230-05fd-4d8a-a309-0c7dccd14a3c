{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nfunction isPointsEq() {\n  var a1 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var a2 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  var isAlignPoint = arguments.length > 2 ? arguments[2] : undefined;\n  if (isAlignPoint) {\n    return a1[0] === a2[0];\n  }\n  return a1[0] === a2[0] && a1[1] === a2[1];\n}\nexport function getAlignPopupClassName(builtinPlacements, prefixCls, align, isAlignPoint) {\n  var points = align.points;\n  var placements = Object.keys(builtinPlacements);\n  for (var i = 0; i < placements.length; i += 1) {\n    var _builtinPlacements$pl;\n    var placement = placements[i];\n    if (isPointsEq((_builtinPlacements$pl = builtinPlacements[placement]) === null || _builtinPlacements$pl === void 0 ? void 0 : _builtinPlacements$pl.points, points, isAlignPoint)) {\n      return \"\".concat(prefixCls, \"-placement-\").concat(placement);\n    }\n  }\n  return '';\n}\n\n/** @deprecated We should not use this if we can refactor all deps */\nexport function getMotion(prefixCls, motion, animation, transitionName) {\n  if (motion) {\n    return motion;\n  }\n  if (animation) {\n    return {\n      motionName: \"\".concat(prefixCls, \"-\").concat(animation)\n    };\n  }\n  if (transitionName) {\n    return {\n      motionName: transitionName\n    };\n  }\n  return null;\n}\nexport function getWin(ele) {\n  return ele.ownerDocument.defaultView;\n}\n\n/**\n * Get all the scrollable parent elements of the element\n * @param ele       The element to be detected\n * @param areaOnly  Only return the parent which will cut visible area\n */\nexport function collectScroller(ele) {\n  var scrollerList = [];\n  var current = ele === null || ele === void 0 ? void 0 : ele.parentElement;\n  var scrollStyle = ['hidden', 'scroll', 'clip', 'auto'];\n  while (current) {\n    var _getWin$getComputedSt = getWin(current).getComputedStyle(current),\n      overflowX = _getWin$getComputedSt.overflowX,\n      overflowY = _getWin$getComputedSt.overflowY,\n      overflow = _getWin$getComputedSt.overflow;\n    if ([overflowX, overflowY, overflow].some(function (o) {\n      return scrollStyle.includes(o);\n    })) {\n      scrollerList.push(current);\n    }\n    current = current.parentElement;\n  }\n  return scrollerList;\n}\nexport function toNum(num) {\n  var defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n  return Number.isNaN(num) ? defaultValue : num;\n}\nfunction getPxValue(val) {\n  return toNum(parseFloat(val), 0);\n}\n/**\n *\n *\n *  **************************************\n *  *              Border                *\n *  *     **************************     *\n *  *     *                  *     *     *\n *  *  B  *                  *  S  *  B  *\n *  *  o  *                  *  c  *  o  *\n *  *  r  *      Content     *  r  *  r  *\n *  *  d  *                  *  o  *  d  *\n *  *  e  *                  *  l  *  e  *\n *  *  r  ********************  l  *  r  *\n *  *     *        Scroll          *     *\n *  *     **************************     *\n *  *              Border                *\n *  **************************************\n *\n */\n/**\n * Get visible area of element\n */\nexport function getVisibleArea(initArea, scrollerList) {\n  var visibleArea = _objectSpread({}, initArea);\n  (scrollerList || []).forEach(function (ele) {\n    if (ele instanceof HTMLBodyElement || ele instanceof HTMLHtmlElement) {\n      return;\n    }\n\n    // Skip if static position which will not affect visible area\n    var _getWin$getComputedSt2 = getWin(ele).getComputedStyle(ele),\n      overflow = _getWin$getComputedSt2.overflow,\n      overflowClipMargin = _getWin$getComputedSt2.overflowClipMargin,\n      borderTopWidth = _getWin$getComputedSt2.borderTopWidth,\n      borderBottomWidth = _getWin$getComputedSt2.borderBottomWidth,\n      borderLeftWidth = _getWin$getComputedSt2.borderLeftWidth,\n      borderRightWidth = _getWin$getComputedSt2.borderRightWidth;\n    var eleRect = ele.getBoundingClientRect();\n    var eleOutHeight = ele.offsetHeight,\n      eleInnerHeight = ele.clientHeight,\n      eleOutWidth = ele.offsetWidth,\n      eleInnerWidth = ele.clientWidth;\n    var borderTopNum = getPxValue(borderTopWidth);\n    var borderBottomNum = getPxValue(borderBottomWidth);\n    var borderLeftNum = getPxValue(borderLeftWidth);\n    var borderRightNum = getPxValue(borderRightWidth);\n    var scaleX = toNum(Math.round(eleRect.width / eleOutWidth * 1000) / 1000);\n    var scaleY = toNum(Math.round(eleRect.height / eleOutHeight * 1000) / 1000);\n\n    // Original visible area\n    var eleScrollWidth = (eleOutWidth - eleInnerWidth - borderLeftNum - borderRightNum) * scaleX;\n    var eleScrollHeight = (eleOutHeight - eleInnerHeight - borderTopNum - borderBottomNum) * scaleY;\n\n    // Cut border size\n    var scaledBorderTopWidth = borderTopNum * scaleY;\n    var scaledBorderBottomWidth = borderBottomNum * scaleY;\n    var scaledBorderLeftWidth = borderLeftNum * scaleX;\n    var scaledBorderRightWidth = borderRightNum * scaleX;\n\n    // Clip margin\n    var clipMarginWidth = 0;\n    var clipMarginHeight = 0;\n    if (overflow === 'clip') {\n      var clipNum = getPxValue(overflowClipMargin);\n      clipMarginWidth = clipNum * scaleX;\n      clipMarginHeight = clipNum * scaleY;\n    }\n\n    // Region\n    var eleLeft = eleRect.x + scaledBorderLeftWidth - clipMarginWidth;\n    var eleTop = eleRect.y + scaledBorderTopWidth - clipMarginHeight;\n    var eleRight = eleLeft + eleRect.width + 2 * clipMarginWidth - scaledBorderLeftWidth - scaledBorderRightWidth - eleScrollWidth;\n    var eleBottom = eleTop + eleRect.height + 2 * clipMarginHeight - scaledBorderTopWidth - scaledBorderBottomWidth - eleScrollHeight;\n    visibleArea.left = Math.max(visibleArea.left, eleLeft);\n    visibleArea.top = Math.max(visibleArea.top, eleTop);\n    visibleArea.right = Math.min(visibleArea.right, eleRight);\n    visibleArea.bottom = Math.min(visibleArea.bottom, eleBottom);\n  });\n  return visibleArea;\n}", "map": {"version": 3, "names": ["_objectSpread", "isPointsEq", "a1", "arguments", "length", "undefined", "a2", "isAlignPoint", "getAlignPopupClassName", "builtinPlacements", "prefixCls", "align", "points", "placements", "Object", "keys", "i", "_builtinPlacements$pl", "placement", "concat", "getMotion", "motion", "animation", "transitionName", "motionName", "getWin", "ele", "ownerDocument", "defaultView", "collectScroller", "scrollerList", "current", "parentElement", "scrollStyle", "_getWin$getComputedSt", "getComputedStyle", "overflowX", "overflowY", "overflow", "some", "o", "includes", "push", "to<PERSON>um", "num", "defaultValue", "Number", "isNaN", "getPxValue", "val", "parseFloat", "getVisibleArea", "initArea", "visibleArea", "for<PERSON>ach", "HTMLBodyElement", "HTMLHtmlElement", "_getWin$getComputedSt2", "overflowClipMargin", "borderTopWidth", "borderBottomWidth", "borderLeftWidth", "borderRightWidth", "eleRect", "getBoundingClientRect", "eleOutHeight", "offsetHeight", "eleInnerHeight", "clientHeight", "eleOutWidth", "offsetWidth", "eleInner<PERSON>idth", "clientWidth", "borderTopNum", "borderBottomNum", "borderLeftNum", "borderRightNum", "scaleX", "Math", "round", "width", "scaleY", "height", "eleScroll<PERSON><PERSON><PERSON>", "eleScrollHeight", "scaledBorderTopWidth", "scaledBorderBottomWidth", "scaledBorderLeftWidth", "scaledBorderRightWidth", "clip<PERSON>argin<PERSON><PERSON><PERSON>", "clipMarginHeight", "clipNum", "eleLeft", "x", "eleTop", "y", "eleRight", "eleBottom", "left", "max", "top", "right", "min", "bottom"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/@rc-component/trigger/es/util.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nfunction isPointsEq() {\n  var a1 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var a2 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  var isAlignPoint = arguments.length > 2 ? arguments[2] : undefined;\n  if (isAlignPoint) {\n    return a1[0] === a2[0];\n  }\n  return a1[0] === a2[0] && a1[1] === a2[1];\n}\nexport function getAlignPopupClassName(builtinPlacements, prefixCls, align, isAlignPoint) {\n  var points = align.points;\n  var placements = Object.keys(builtinPlacements);\n  for (var i = 0; i < placements.length; i += 1) {\n    var _builtinPlacements$pl;\n    var placement = placements[i];\n    if (isPointsEq((_builtinPlacements$pl = builtinPlacements[placement]) === null || _builtinPlacements$pl === void 0 ? void 0 : _builtinPlacements$pl.points, points, isAlignPoint)) {\n      return \"\".concat(prefixCls, \"-placement-\").concat(placement);\n    }\n  }\n  return '';\n}\n\n/** @deprecated We should not use this if we can refactor all deps */\nexport function getMotion(prefixCls, motion, animation, transitionName) {\n  if (motion) {\n    return motion;\n  }\n  if (animation) {\n    return {\n      motionName: \"\".concat(prefixCls, \"-\").concat(animation)\n    };\n  }\n  if (transitionName) {\n    return {\n      motionName: transitionName\n    };\n  }\n  return null;\n}\nexport function getWin(ele) {\n  return ele.ownerDocument.defaultView;\n}\n\n/**\n * Get all the scrollable parent elements of the element\n * @param ele       The element to be detected\n * @param areaOnly  Only return the parent which will cut visible area\n */\nexport function collectScroller(ele) {\n  var scrollerList = [];\n  var current = ele === null || ele === void 0 ? void 0 : ele.parentElement;\n  var scrollStyle = ['hidden', 'scroll', 'clip', 'auto'];\n  while (current) {\n    var _getWin$getComputedSt = getWin(current).getComputedStyle(current),\n      overflowX = _getWin$getComputedSt.overflowX,\n      overflowY = _getWin$getComputedSt.overflowY,\n      overflow = _getWin$getComputedSt.overflow;\n    if ([overflowX, overflowY, overflow].some(function (o) {\n      return scrollStyle.includes(o);\n    })) {\n      scrollerList.push(current);\n    }\n    current = current.parentElement;\n  }\n  return scrollerList;\n}\nexport function toNum(num) {\n  var defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n  return Number.isNaN(num) ? defaultValue : num;\n}\nfunction getPxValue(val) {\n  return toNum(parseFloat(val), 0);\n}\n/**\n *\n *\n *  **************************************\n *  *              Border                *\n *  *     **************************     *\n *  *     *                  *     *     *\n *  *  B  *                  *  S  *  B  *\n *  *  o  *                  *  c  *  o  *\n *  *  r  *      Content     *  r  *  r  *\n *  *  d  *                  *  o  *  d  *\n *  *  e  *                  *  l  *  e  *\n *  *  r  ********************  l  *  r  *\n *  *     *        Scroll          *     *\n *  *     **************************     *\n *  *              Border                *\n *  **************************************\n *\n */\n/**\n * Get visible area of element\n */\nexport function getVisibleArea(initArea, scrollerList) {\n  var visibleArea = _objectSpread({}, initArea);\n  (scrollerList || []).forEach(function (ele) {\n    if (ele instanceof HTMLBodyElement || ele instanceof HTMLHtmlElement) {\n      return;\n    }\n\n    // Skip if static position which will not affect visible area\n    var _getWin$getComputedSt2 = getWin(ele).getComputedStyle(ele),\n      overflow = _getWin$getComputedSt2.overflow,\n      overflowClipMargin = _getWin$getComputedSt2.overflowClipMargin,\n      borderTopWidth = _getWin$getComputedSt2.borderTopWidth,\n      borderBottomWidth = _getWin$getComputedSt2.borderBottomWidth,\n      borderLeftWidth = _getWin$getComputedSt2.borderLeftWidth,\n      borderRightWidth = _getWin$getComputedSt2.borderRightWidth;\n    var eleRect = ele.getBoundingClientRect();\n    var eleOutHeight = ele.offsetHeight,\n      eleInnerHeight = ele.clientHeight,\n      eleOutWidth = ele.offsetWidth,\n      eleInnerWidth = ele.clientWidth;\n    var borderTopNum = getPxValue(borderTopWidth);\n    var borderBottomNum = getPxValue(borderBottomWidth);\n    var borderLeftNum = getPxValue(borderLeftWidth);\n    var borderRightNum = getPxValue(borderRightWidth);\n    var scaleX = toNum(Math.round(eleRect.width / eleOutWidth * 1000) / 1000);\n    var scaleY = toNum(Math.round(eleRect.height / eleOutHeight * 1000) / 1000);\n\n    // Original visible area\n    var eleScrollWidth = (eleOutWidth - eleInnerWidth - borderLeftNum - borderRightNum) * scaleX;\n    var eleScrollHeight = (eleOutHeight - eleInnerHeight - borderTopNum - borderBottomNum) * scaleY;\n\n    // Cut border size\n    var scaledBorderTopWidth = borderTopNum * scaleY;\n    var scaledBorderBottomWidth = borderBottomNum * scaleY;\n    var scaledBorderLeftWidth = borderLeftNum * scaleX;\n    var scaledBorderRightWidth = borderRightNum * scaleX;\n\n    // Clip margin\n    var clipMarginWidth = 0;\n    var clipMarginHeight = 0;\n    if (overflow === 'clip') {\n      var clipNum = getPxValue(overflowClipMargin);\n      clipMarginWidth = clipNum * scaleX;\n      clipMarginHeight = clipNum * scaleY;\n    }\n\n    // Region\n    var eleLeft = eleRect.x + scaledBorderLeftWidth - clipMarginWidth;\n    var eleTop = eleRect.y + scaledBorderTopWidth - clipMarginHeight;\n    var eleRight = eleLeft + eleRect.width + 2 * clipMarginWidth - scaledBorderLeftWidth - scaledBorderRightWidth - eleScrollWidth;\n    var eleBottom = eleTop + eleRect.height + 2 * clipMarginHeight - scaledBorderTopWidth - scaledBorderBottomWidth - eleScrollHeight;\n    visibleArea.left = Math.max(visibleArea.left, eleLeft);\n    visibleArea.top = Math.max(visibleArea.top, eleTop);\n    visibleArea.right = Math.min(visibleArea.right, eleRight);\n    visibleArea.bottom = Math.min(visibleArea.bottom, eleBottom);\n  });\n  return visibleArea;\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,SAASC,UAAUA,CAAA,EAAG;EACpB,IAAIC,EAAE,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EAC/E,IAAIG,EAAE,GAAGH,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EAC/E,IAAII,YAAY,GAAGJ,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGE,SAAS;EAClE,IAAIE,YAAY,EAAE;IAChB,OAAOL,EAAE,CAAC,CAAC,CAAC,KAAKI,EAAE,CAAC,CAAC,CAAC;EACxB;EACA,OAAOJ,EAAE,CAAC,CAAC,CAAC,KAAKI,EAAE,CAAC,CAAC,CAAC,IAAIJ,EAAE,CAAC,CAAC,CAAC,KAAKI,EAAE,CAAC,CAAC,CAAC;AAC3C;AACA,OAAO,SAASE,sBAAsBA,CAACC,iBAAiB,EAAEC,SAAS,EAAEC,KAAK,EAAEJ,YAAY,EAAE;EACxF,IAAIK,MAAM,GAAGD,KAAK,CAACC,MAAM;EACzB,IAAIC,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACN,iBAAiB,CAAC;EAC/C,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,UAAU,CAACT,MAAM,EAAEY,CAAC,IAAI,CAAC,EAAE;IAC7C,IAAIC,qBAAqB;IACzB,IAAIC,SAAS,GAAGL,UAAU,CAACG,CAAC,CAAC;IAC7B,IAAIf,UAAU,CAAC,CAACgB,qBAAqB,GAAGR,iBAAiB,CAACS,SAAS,CAAC,MAAM,IAAI,IAAID,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACL,MAAM,EAAEA,MAAM,EAAEL,YAAY,CAAC,EAAE;MACjL,OAAO,EAAE,CAACY,MAAM,CAACT,SAAS,EAAE,aAAa,CAAC,CAACS,MAAM,CAACD,SAAS,CAAC;IAC9D;EACF;EACA,OAAO,EAAE;AACX;;AAEA;AACA,OAAO,SAASE,SAASA,CAACV,SAAS,EAAEW,MAAM,EAAEC,SAAS,EAAEC,cAAc,EAAE;EACtE,IAAIF,MAAM,EAAE;IACV,OAAOA,MAAM;EACf;EACA,IAAIC,SAAS,EAAE;IACb,OAAO;MACLE,UAAU,EAAE,EAAE,CAACL,MAAM,CAACT,SAAS,EAAE,GAAG,CAAC,CAACS,MAAM,CAACG,SAAS;IACxD,CAAC;EACH;EACA,IAAIC,cAAc,EAAE;IAClB,OAAO;MACLC,UAAU,EAAED;IACd,CAAC;EACH;EACA,OAAO,IAAI;AACb;AACA,OAAO,SAASE,MAAMA,CAACC,GAAG,EAAE;EAC1B,OAAOA,GAAG,CAACC,aAAa,CAACC,WAAW;AACtC;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACH,GAAG,EAAE;EACnC,IAAII,YAAY,GAAG,EAAE;EACrB,IAAIC,OAAO,GAAGL,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACM,aAAa;EACzE,IAAIC,WAAW,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC;EACtD,OAAOF,OAAO,EAAE;IACd,IAAIG,qBAAqB,GAAGT,MAAM,CAACM,OAAO,CAAC,CAACI,gBAAgB,CAACJ,OAAO,CAAC;MACnEK,SAAS,GAAGF,qBAAqB,CAACE,SAAS;MAC3CC,SAAS,GAAGH,qBAAqB,CAACG,SAAS;MAC3CC,QAAQ,GAAGJ,qBAAqB,CAACI,QAAQ;IAC3C,IAAI,CAACF,SAAS,EAAEC,SAAS,EAAEC,QAAQ,CAAC,CAACC,IAAI,CAAC,UAAUC,CAAC,EAAE;MACrD,OAAOP,WAAW,CAACQ,QAAQ,CAACD,CAAC,CAAC;IAChC,CAAC,CAAC,EAAE;MACFV,YAAY,CAACY,IAAI,CAACX,OAAO,CAAC;IAC5B;IACAA,OAAO,GAAGA,OAAO,CAACC,aAAa;EACjC;EACA,OAAOF,YAAY;AACrB;AACA,OAAO,SAASa,KAAKA,CAACC,GAAG,EAAE;EACzB,IAAIC,YAAY,GAAG1C,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EACxF,OAAO2C,MAAM,CAACC,KAAK,CAACH,GAAG,CAAC,GAAGC,YAAY,GAAGD,GAAG;AAC/C;AACA,SAASI,UAAUA,CAACC,GAAG,EAAE;EACvB,OAAON,KAAK,CAACO,UAAU,CAACD,GAAG,CAAC,EAAE,CAAC,CAAC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,cAAcA,CAACC,QAAQ,EAAEtB,YAAY,EAAE;EACrD,IAAIuB,WAAW,GAAGrD,aAAa,CAAC,CAAC,CAAC,EAAEoD,QAAQ,CAAC;EAC7C,CAACtB,YAAY,IAAI,EAAE,EAAEwB,OAAO,CAAC,UAAU5B,GAAG,EAAE;IAC1C,IAAIA,GAAG,YAAY6B,eAAe,IAAI7B,GAAG,YAAY8B,eAAe,EAAE;MACpE;IACF;;IAEA;IACA,IAAIC,sBAAsB,GAAGhC,MAAM,CAACC,GAAG,CAAC,CAACS,gBAAgB,CAACT,GAAG,CAAC;MAC5DY,QAAQ,GAAGmB,sBAAsB,CAACnB,QAAQ;MAC1CoB,kBAAkB,GAAGD,sBAAsB,CAACC,kBAAkB;MAC9DC,cAAc,GAAGF,sBAAsB,CAACE,cAAc;MACtDC,iBAAiB,GAAGH,sBAAsB,CAACG,iBAAiB;MAC5DC,eAAe,GAAGJ,sBAAsB,CAACI,eAAe;MACxDC,gBAAgB,GAAGL,sBAAsB,CAACK,gBAAgB;IAC5D,IAAIC,OAAO,GAAGrC,GAAG,CAACsC,qBAAqB,CAAC,CAAC;IACzC,IAAIC,YAAY,GAAGvC,GAAG,CAACwC,YAAY;MACjCC,cAAc,GAAGzC,GAAG,CAAC0C,YAAY;MACjCC,WAAW,GAAG3C,GAAG,CAAC4C,WAAW;MAC7BC,aAAa,GAAG7C,GAAG,CAAC8C,WAAW;IACjC,IAAIC,YAAY,GAAGzB,UAAU,CAACW,cAAc,CAAC;IAC7C,IAAIe,eAAe,GAAG1B,UAAU,CAACY,iBAAiB,CAAC;IACnD,IAAIe,aAAa,GAAG3B,UAAU,CAACa,eAAe,CAAC;IAC/C,IAAIe,cAAc,GAAG5B,UAAU,CAACc,gBAAgB,CAAC;IACjD,IAAIe,MAAM,GAAGlC,KAAK,CAACmC,IAAI,CAACC,KAAK,CAAChB,OAAO,CAACiB,KAAK,GAAGX,WAAW,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;IACzE,IAAIY,MAAM,GAAGtC,KAAK,CAACmC,IAAI,CAACC,KAAK,CAAChB,OAAO,CAACmB,MAAM,GAAGjB,YAAY,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;;IAE3E;IACA,IAAIkB,cAAc,GAAG,CAACd,WAAW,GAAGE,aAAa,GAAGI,aAAa,GAAGC,cAAc,IAAIC,MAAM;IAC5F,IAAIO,eAAe,GAAG,CAACnB,YAAY,GAAGE,cAAc,GAAGM,YAAY,GAAGC,eAAe,IAAIO,MAAM;;IAE/F;IACA,IAAII,oBAAoB,GAAGZ,YAAY,GAAGQ,MAAM;IAChD,IAAIK,uBAAuB,GAAGZ,eAAe,GAAGO,MAAM;IACtD,IAAIM,qBAAqB,GAAGZ,aAAa,GAAGE,MAAM;IAClD,IAAIW,sBAAsB,GAAGZ,cAAc,GAAGC,MAAM;;IAEpD;IACA,IAAIY,eAAe,GAAG,CAAC;IACvB,IAAIC,gBAAgB,GAAG,CAAC;IACxB,IAAIpD,QAAQ,KAAK,MAAM,EAAE;MACvB,IAAIqD,OAAO,GAAG3C,UAAU,CAACU,kBAAkB,CAAC;MAC5C+B,eAAe,GAAGE,OAAO,GAAGd,MAAM;MAClCa,gBAAgB,GAAGC,OAAO,GAAGV,MAAM;IACrC;;IAEA;IACA,IAAIW,OAAO,GAAG7B,OAAO,CAAC8B,CAAC,GAAGN,qBAAqB,GAAGE,eAAe;IACjE,IAAIK,MAAM,GAAG/B,OAAO,CAACgC,CAAC,GAAGV,oBAAoB,GAAGK,gBAAgB;IAChE,IAAIM,QAAQ,GAAGJ,OAAO,GAAG7B,OAAO,CAACiB,KAAK,GAAG,CAAC,GAAGS,eAAe,GAAGF,qBAAqB,GAAGC,sBAAsB,GAAGL,cAAc;IAC9H,IAAIc,SAAS,GAAGH,MAAM,GAAG/B,OAAO,CAACmB,MAAM,GAAG,CAAC,GAAGQ,gBAAgB,GAAGL,oBAAoB,GAAGC,uBAAuB,GAAGF,eAAe;IACjI/B,WAAW,CAAC6C,IAAI,GAAGpB,IAAI,CAACqB,GAAG,CAAC9C,WAAW,CAAC6C,IAAI,EAAEN,OAAO,CAAC;IACtDvC,WAAW,CAAC+C,GAAG,GAAGtB,IAAI,CAACqB,GAAG,CAAC9C,WAAW,CAAC+C,GAAG,EAAEN,MAAM,CAAC;IACnDzC,WAAW,CAACgD,KAAK,GAAGvB,IAAI,CAACwB,GAAG,CAACjD,WAAW,CAACgD,KAAK,EAAEL,QAAQ,CAAC;IACzD3C,WAAW,CAACkD,MAAM,GAAGzB,IAAI,CAACwB,GAAG,CAACjD,WAAW,CAACkD,MAAM,EAAEN,SAAS,CAAC;EAC9D,CAAC,CAAC;EACF,OAAO5C,WAAW;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}