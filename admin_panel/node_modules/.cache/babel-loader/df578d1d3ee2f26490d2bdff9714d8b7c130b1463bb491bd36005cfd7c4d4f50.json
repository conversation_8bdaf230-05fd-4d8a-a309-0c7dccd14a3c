{"ast": null, "code": "import * as React from 'react';\nexport var RefContext = /*#__PURE__*/React.createContext({});", "map": {"version": 3, "names": ["React", "RefContext", "createContext"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/rc-dialog/es/context.js"], "sourcesContent": ["import * as React from 'react';\nexport var RefContext = /*#__PURE__*/React.createContext({});"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,IAAIC,UAAU,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}