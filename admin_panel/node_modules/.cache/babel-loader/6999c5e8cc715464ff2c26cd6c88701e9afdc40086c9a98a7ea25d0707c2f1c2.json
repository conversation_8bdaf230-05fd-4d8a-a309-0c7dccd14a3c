{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport * as React from 'react';\nexport default function Mask(props) {\n  var prefixCls = props.prefixCls,\n    open = props.open,\n    zIndex = props.zIndex,\n    mask = props.mask,\n    motion = props.motion;\n  if (!mask) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(CSSMotion, _extends({}, motion, {\n    motionAppear: true,\n    visible: open,\n    removeOnLeave: true\n  }), function (_ref) {\n    var className = _ref.className;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      style: {\n        zIndex: zIndex\n      },\n      className: classNames(\"\".concat(prefixCls, \"-mask\"), className)\n    });\n  });\n}", "map": {"version": 3, "names": ["_extends", "classNames", "CSSMotion", "React", "Mask", "props", "prefixCls", "open", "zIndex", "mask", "motion", "createElement", "motionAppear", "visible", "removeOnLeave", "_ref", "className", "style", "concat"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/@rc-component/trigger/es/Popup/Mask.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport * as React from 'react';\nexport default function Mask(props) {\n  var prefixCls = props.prefixCls,\n    open = props.open,\n    zIndex = props.zIndex,\n    mask = props.mask,\n    motion = props.motion;\n  if (!mask) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(CSSMotion, _extends({}, motion, {\n    motionAppear: true,\n    visible: open,\n    removeOnLeave: true\n  }), function (_ref) {\n    var className = _ref.className;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      style: {\n        zIndex: zIndex\n      },\n      className: classNames(\"\".concat(prefixCls, \"-mask\"), className)\n    });\n  });\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,IAAIA,CAACC,KAAK,EAAE;EAClC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,IAAI,GAAGF,KAAK,CAACE,IAAI;IACjBC,MAAM,GAAGH,KAAK,CAACG,MAAM;IACrBC,IAAI,GAAGJ,KAAK,CAACI,IAAI;IACjBC,MAAM,GAAGL,KAAK,CAACK,MAAM;EACvB,IAAI,CAACD,IAAI,EAAE;IACT,OAAO,IAAI;EACb;EACA,OAAO,aAAaN,KAAK,CAACQ,aAAa,CAACT,SAAS,EAAEF,QAAQ,CAAC,CAAC,CAAC,EAAEU,MAAM,EAAE;IACtEE,YAAY,EAAE,IAAI;IAClBC,OAAO,EAAEN,IAAI;IACbO,aAAa,EAAE;EACjB,CAAC,CAAC,EAAE,UAAUC,IAAI,EAAE;IAClB,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC9B,OAAO,aAAab,KAAK,CAACQ,aAAa,CAAC,KAAK,EAAE;MAC7CM,KAAK,EAAE;QACLT,MAAM,EAAEA;MACV,CAAC;MACDQ,SAAS,EAAEf,UAAU,CAAC,EAAE,CAACiB,MAAM,CAACZ,SAAS,EAAE,OAAO,CAAC,EAAEU,SAAS;IAChE,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}