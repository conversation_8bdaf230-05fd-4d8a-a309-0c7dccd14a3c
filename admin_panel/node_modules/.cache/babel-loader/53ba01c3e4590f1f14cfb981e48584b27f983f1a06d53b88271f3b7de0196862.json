{"ast": null, "code": "import canUseDom from \"rc-util/es/Dom/canUseDom\";\nimport { useEffect, useLayoutEffect } from 'react';\n\n// It's safe to use `useLayoutEffect` but the warning is annoying\nvar useIsomorphicLayoutEffect = canUseDom() ? useLayoutEffect : useEffect;\nexport default useIsomorphicLayoutEffect;", "map": {"version": 3, "names": ["canUseDom", "useEffect", "useLayoutEffect", "useIsomorphicLayoutEffect"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js"], "sourcesContent": ["import canUseDom from \"rc-util/es/Dom/canUseDom\";\nimport { useEffect, useLayoutEffect } from 'react';\n\n// It's safe to use `useLayoutEffect` but the warning is annoying\nvar useIsomorphicLayoutEffect = canUseDom() ? useLayoutEffect : useEffect;\nexport default useIsomorphicLayoutEffect;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,0BAA0B;AAChD,SAASC,SAAS,EAAEC,eAAe,QAAQ,OAAO;;AAElD;AACA,IAAIC,yBAAyB,GAAGH,SAAS,CAAC,CAAC,GAAGE,eAAe,GAAGD,SAAS;AACzE,eAAeE,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}