{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useMergedState } from 'rc-util';\nimport { useMemo } from 'react';\nimport { generateColor } from \"../util\";\nvar useColorState = function useColorState(defaultValue, value) {\n  var _useMergedState = useMergedState(defaultValue, {\n      value: value\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedValue = _useMergedState2[0],\n    setValue = _useMergedState2[1];\n  var color = useMemo(function () {\n    return generateColor(mergedValue);\n  }, [mergedValue]);\n  return [color, setValue];\n};\nexport default useColorState;", "map": {"version": 3, "names": ["_slicedToArray", "useMergedState", "useMemo", "generateColor", "useColorState", "defaultValue", "value", "_useMergedState", "_useMergedState2", "mergedValue", "setValue", "color"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/@rc-component/color-picker/es/hooks/useColorState.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useMergedState } from 'rc-util';\nimport { useMemo } from 'react';\nimport { generateColor } from \"../util\";\nvar useColorState = function useColorState(defaultValue, value) {\n  var _useMergedState = useMergedState(defaultValue, {\n      value: value\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedValue = _useMergedState2[0],\n    setValue = _useMergedState2[1];\n  var color = useMemo(function () {\n    return generateColor(mergedValue);\n  }, [mergedValue]);\n  return [color, setValue];\n};\nexport default useColorState;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,cAAc,QAAQ,SAAS;AACxC,SAASC,OAAO,QAAQ,OAAO;AAC/B,SAASC,aAAa,QAAQ,SAAS;AACvC,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,YAAY,EAAEC,KAAK,EAAE;EAC9D,IAAIC,eAAe,GAAGN,cAAc,CAACI,YAAY,EAAE;MAC/CC,KAAK,EAAEA;IACT,CAAC,CAAC;IACFE,gBAAgB,GAAGR,cAAc,CAACO,eAAe,EAAE,CAAC,CAAC;IACrDE,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,QAAQ,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAChC,IAAIG,KAAK,GAAGT,OAAO,CAAC,YAAY;IAC9B,OAAOC,aAAa,CAACM,WAAW,CAAC;EACnC,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EACjB,OAAO,CAACE,KAAK,EAAED,QAAQ,CAAC;AAC1B,CAAC;AACD,eAAeN,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}