{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport VideoCameraAddOutlinedSvg from \"@ant-design/icons-svg/es/asn/VideoCameraAddOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar VideoCameraAddOutlined = function VideoCameraAddOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: VideoCameraAddOutlinedSvg\n  }));\n};\n\n/**![video-camera-add](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik0zNjggNzI0SDI1MlY2MDhjMC00LjQtMy42LTgtOC04aC00OGMtNC40IDAtOCAzLjYtOCA4djExNkg3MmMtNC40IDAtOCAzLjYtOCA4djQ4YzAgNC40IDMuNiA4IDggOGgxMTZ2MTE2YzAgNC40IDMuNiA4IDggOGg0OGM0LjQgMCA4LTMuNiA4LThWNzg4aDExNmM0LjQgMCA4LTMuNiA4LTh2LTQ4YzAtNC40LTMuNi04LTgtOHoiIC8+PHBhdGggZD0iTTkxMiAzMDIuM0w3ODQgMzc2VjIyNGMwLTM1LjMtMjguNy02NC02NC02NEgxMjhjLTM1LjMgMC02NCAyOC43LTY0IDY0djM1Mmg3MlYyMzJoNTc2djU2MEg0NDh2NzJoMjcyYzM1LjMgMCA2NC0yOC43IDY0LTY0VjY0OGwxMjggNzMuN2MyMS4zIDEyLjMgNDgtMy4xIDQ4LTI3LjZWMzMwYzAtMjQuNi0yNi43LTQwLTQ4LTI3Ljd6TTg4OCA2MjVsLTEwNC01OS44VjQ1OC45TDg4OCAzOTl2MjI2eiIgLz48cGF0aCBkPSJNMzIwIDM2MGM0LjQgMCA4LTMuNiA4LTh2LTQ4YzAtNC40LTMuNi04LTgtOEgyMDhjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDhoMTEyeiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(VideoCameraAddOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'VideoCameraAddOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "VideoCameraAddOutlinedSvg", "AntdIcon", "VideoCameraAddOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/antd/node_modules/@ant-design/icons/es/icons/VideoCameraAddOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport VideoCameraAddOutlinedSvg from \"@ant-design/icons-svg/es/asn/VideoCameraAddOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar VideoCameraAddOutlined = function VideoCameraAddOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: VideoCameraAddOutlinedSvg\n  }));\n};\n\n/**![video-camera-add](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik0zNjggNzI0SDI1MlY2MDhjMC00LjQtMy42LTgtOC04aC00OGMtNC40IDAtOCAzLjYtOCA4djExNkg3MmMtNC40IDAtOCAzLjYtOCA4djQ4YzAgNC40IDMuNiA4IDggOGgxMTZ2MTE2YzAgNC40IDMuNiA4IDggOGg0OGM0LjQgMCA4LTMuNiA4LThWNzg4aDExNmM0LjQgMCA4LTMuNiA4LTh2LTQ4YzAtNC40LTMuNi04LTgtOHoiIC8+PHBhdGggZD0iTTkxMiAzMDIuM0w3ODQgMzc2VjIyNGMwLTM1LjMtMjguNy02NC02NC02NEgxMjhjLTM1LjMgMC02NCAyOC43LTY0IDY0djM1Mmg3MlYyMzJoNTc2djU2MEg0NDh2NzJoMjcyYzM1LjMgMCA2NC0yOC43IDY0LTY0VjY0OGwxMjggNzMuN2MyMS4zIDEyLjMgNDgtMy4xIDQ4LTI3LjZWMzMwYzAtMjQuNi0yNi43LTQwLTQ4LTI3Ljd6TTg4OCA2MjVsLTEwNC01OS44VjQ1OC45TDg4OCAzOTl2MjI2eiIgLz48cGF0aCBkPSJNMzIwIDM2MGM0LjQgMCA4LTMuNiA4LTh2LTQ4YzAtNC40LTMuNi04LTgtOEgyMDhjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDhoMTEyeiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(VideoCameraAddOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'VideoCameraAddOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,yBAAyB,MAAM,qDAAqD;AAC3F,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,sBAAsB,GAAG,SAASA,sBAAsBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACvE,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,sBAAsB,CAAC;AACnE,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,wBAAwB;AAChD;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}