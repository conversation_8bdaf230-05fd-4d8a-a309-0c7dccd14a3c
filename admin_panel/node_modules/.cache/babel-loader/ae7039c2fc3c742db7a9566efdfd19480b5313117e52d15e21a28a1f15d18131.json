{"ast": null, "code": "export function getMaskRange(key) {\n  var PresetRange = {\n    YYYY: [0, 9999, new Date().getFullYear()],\n    MM: [1, 12],\n    DD: [1, 31],\n    HH: [0, 23],\n    mm: [0, 59],\n    ss: [0, 59],\n    SSS: [0, 999]\n  };\n  return PresetRange[key];\n}", "map": {"version": 3, "names": ["getMaskRange", "key", "PresetRange", "YYYY", "Date", "getFullYear", "MM", "DD", "HH", "mm", "ss", "SSS"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/rc-picker/es/PickerInput/Selector/util.js"], "sourcesContent": ["export function getMaskRange(key) {\n  var PresetRange = {\n    YYYY: [0, 9999, new Date().getFullYear()],\n    MM: [1, 12],\n    DD: [1, 31],\n    HH: [0, 23],\n    mm: [0, 59],\n    ss: [0, 59],\n    SSS: [0, 999]\n  };\n  return PresetRange[key];\n}"], "mappings": "AAAA,OAAO,SAASA,YAAYA,CAACC,GAAG,EAAE;EAChC,IAAIC,WAAW,GAAG;IAChBC,IAAI,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;IACzCC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IACXC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IACXC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IACXC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IACXC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IACXC,GAAG,EAAE,CAAC,CAAC,EAAE,GAAG;EACd,CAAC;EACD,OAAOT,WAAW,CAACD,GAAG,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}