{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport TranslationOutlinedSvg from \"@ant-design/icons-svg/es/asn/TranslationOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar TranslationOutlined = function TranslationOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: TranslationOutlinedSvg\n  }));\n};\n\n/**![translation](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik0xNDAgMTg4aDU4NHYxNjRoNzZWMTQ0YzAtMTcuNy0xNC4zLTMyLTMyLTMySDk2Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNTQ0di03NkgxNDBWMTg4eiIgLz48cGF0aCBkPSJNNDE0LjMgMjU2aC02MC42Yy0zLjQgMC02LjQgMi4yLTcuNiA1LjRMMjE5IDYyOS40Yy0uMy44LS40IDEuNy0uNCAyLjYgMCA0LjQgMy42IDggOCA4aDU1LjFjMy40IDAgNi40LTIuMiA3LjYtNS40TDMyMiA1NDBoMTk2LjJMNDIyIDI2MS40YTguNDIgOC40MiAwIDAwLTcuNy01LjR6bTEyLjQgMjI4aC04NS41TDM4NCAzNjAuMiA0MjYuNyA0ODR6TTkzNiA1MjhIODAwdi05M2MwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2OTNINTkyYy0xMy4zIDAtMjQgMTAuNy0yNCAyNHYxNzZjMCAxMy4zIDEwLjcgMjQgMjQgMjRoMTM2djE1MmMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04Vjc1MmgxMzZjMTMuMyAwIDI0LTEwLjcgMjQtMjRWNTUyYzAtMTMuMy0xMC43LTI0LTI0LTI0ek03MjggNjgwaC04OHYtODBoODh2ODB6bTE2MCAwaC04OHYtODBoODh2ODB6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(TranslationOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'TranslationOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "TranslationOutlinedSvg", "AntdIcon", "TranslationOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/antd/node_modules/@ant-design/icons/es/icons/TranslationOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport TranslationOutlinedSvg from \"@ant-design/icons-svg/es/asn/TranslationOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar TranslationOutlined = function TranslationOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: TranslationOutlinedSvg\n  }));\n};\n\n/**![translation](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik0xNDAgMTg4aDU4NHYxNjRoNzZWMTQ0YzAtMTcuNy0xNC4zLTMyLTMyLTMySDk2Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNTQ0di03NkgxNDBWMTg4eiIgLz48cGF0aCBkPSJNNDE0LjMgMjU2aC02MC42Yy0zLjQgMC02LjQgMi4yLTcuNiA1LjRMMjE5IDYyOS40Yy0uMy44LS40IDEuNy0uNCAyLjYgMCA0LjQgMy42IDggOCA4aDU1LjFjMy40IDAgNi40LTIuMiA3LjYtNS40TDMyMiA1NDBoMTk2LjJMNDIyIDI2MS40YTguNDIgOC40MiAwIDAwLTcuNy01LjR6bTEyLjQgMjI4aC04NS41TDM4NCAzNjAuMiA0MjYuNyA0ODR6TTkzNiA1MjhIODAwdi05M2MwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2OTNINTkyYy0xMy4zIDAtMjQgMTAuNy0yNCAyNHYxNzZjMCAxMy4zIDEwLjcgMjQgMjQgMjRoMTM2djE1MmMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04Vjc1MmgxMzZjMTMuMyAwIDI0LTEwLjcgMjQtMjRWNTUyYzAtMTMuMy0xMC43LTI0LTI0LTI0ek03MjggNjgwaC04OHYtODBoODh2ODB6bTE2MCAwaC04OHYtODBoODh2ODB6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(TranslationOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'TranslationOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjE,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,mBAAmB,CAAC;AAChE,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,qBAAqB;AAC7C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}