{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport AbstractCalculator from \"./calculator\";\nvar NumCalculator = /*#__PURE__*/function (_AbstractCalculator) {\n  _inherits(NumCalculator, _AbstractCalculator);\n  var _super = _createSuper(NumCalculator);\n  function NumCalculator(num) {\n    var _this;\n    _classCallCheck(this, NumCalculator);\n    _this = _super.call(this);\n    _defineProperty(_assertThisInitialized(_this), \"result\", 0);\n    if (num instanceof NumCalculator) {\n      _this.result = num.result;\n    } else if (typeof num === 'number') {\n      _this.result = num;\n    }\n    return _this;\n  }\n  _createClass(NumCalculator, [{\n    key: \"add\",\n    value: function add(num) {\n      if (num instanceof NumCalculator) {\n        this.result += num.result;\n      } else if (typeof num === 'number') {\n        this.result += num;\n      }\n      return this;\n    }\n  }, {\n    key: \"sub\",\n    value: function sub(num) {\n      if (num instanceof NumCalculator) {\n        this.result -= num.result;\n      } else if (typeof num === 'number') {\n        this.result -= num;\n      }\n      return this;\n    }\n  }, {\n    key: \"mul\",\n    value: function mul(num) {\n      if (num instanceof NumCalculator) {\n        this.result *= num.result;\n      } else if (typeof num === 'number') {\n        this.result *= num;\n      }\n      return this;\n    }\n  }, {\n    key: \"div\",\n    value: function div(num) {\n      if (num instanceof NumCalculator) {\n        this.result /= num.result;\n      } else if (typeof num === 'number') {\n        this.result /= num;\n      }\n      return this;\n    }\n  }, {\n    key: \"equal\",\n    value: function equal() {\n      return this.result;\n    }\n  }]);\n  return NumCalculator;\n}(AbstractCalculator);\nexport default NumCalculator;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_assertThisInitialized", "_inherits", "_createSuper", "_defineProperty", "AbstractCalculator", "NumCalculator", "_AbstractCalculator", "_super", "num", "_this", "call", "result", "key", "value", "add", "sub", "mul", "div", "equal"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/@ant-design/cssinjs-utils/es/util/calc/NumCalculator.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport AbstractCalculator from \"./calculator\";\nvar NumCalculator = /*#__PURE__*/function (_AbstractCalculator) {\n  _inherits(NumCalculator, _AbstractCalculator);\n  var _super = _createSuper(NumCalculator);\n  function NumCalculator(num) {\n    var _this;\n    _classCallCheck(this, NumCalculator);\n    _this = _super.call(this);\n    _defineProperty(_assertThisInitialized(_this), \"result\", 0);\n    if (num instanceof NumCalculator) {\n      _this.result = num.result;\n    } else if (typeof num === 'number') {\n      _this.result = num;\n    }\n    return _this;\n  }\n  _createClass(NumCalculator, [{\n    key: \"add\",\n    value: function add(num) {\n      if (num instanceof NumCalculator) {\n        this.result += num.result;\n      } else if (typeof num === 'number') {\n        this.result += num;\n      }\n      return this;\n    }\n  }, {\n    key: \"sub\",\n    value: function sub(num) {\n      if (num instanceof NumCalculator) {\n        this.result -= num.result;\n      } else if (typeof num === 'number') {\n        this.result -= num;\n      }\n      return this;\n    }\n  }, {\n    key: \"mul\",\n    value: function mul(num) {\n      if (num instanceof NumCalculator) {\n        this.result *= num.result;\n      } else if (typeof num === 'number') {\n        this.result *= num;\n      }\n      return this;\n    }\n  }, {\n    key: \"div\",\n    value: function div(num) {\n      if (num instanceof NumCalculator) {\n        this.result /= num.result;\n      } else if (typeof num === 'number') {\n        this.result /= num;\n      }\n      return this;\n    }\n  }, {\n    key: \"equal\",\n    value: function equal() {\n      return this.result;\n    }\n  }]);\n  return NumCalculator;\n}(AbstractCalculator);\nexport default NumCalculator;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,kBAAkB,MAAM,cAAc;AAC7C,IAAIC,aAAa,GAAG,aAAa,UAAUC,mBAAmB,EAAE;EAC9DL,SAAS,CAACI,aAAa,EAAEC,mBAAmB,CAAC;EAC7C,IAAIC,MAAM,GAAGL,YAAY,CAACG,aAAa,CAAC;EACxC,SAASA,aAAaA,CAACG,GAAG,EAAE;IAC1B,IAAIC,KAAK;IACTX,eAAe,CAAC,IAAI,EAAEO,aAAa,CAAC;IACpCI,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC;IACzBP,eAAe,CAACH,sBAAsB,CAACS,KAAK,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;IAC3D,IAAID,GAAG,YAAYH,aAAa,EAAE;MAChCI,KAAK,CAACE,MAAM,GAAGH,GAAG,CAACG,MAAM;IAC3B,CAAC,MAAM,IAAI,OAAOH,GAAG,KAAK,QAAQ,EAAE;MAClCC,KAAK,CAACE,MAAM,GAAGH,GAAG;IACpB;IACA,OAAOC,KAAK;EACd;EACAV,YAAY,CAACM,aAAa,EAAE,CAAC;IAC3BO,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASC,GAAGA,CAACN,GAAG,EAAE;MACvB,IAAIA,GAAG,YAAYH,aAAa,EAAE;QAChC,IAAI,CAACM,MAAM,IAAIH,GAAG,CAACG,MAAM;MAC3B,CAAC,MAAM,IAAI,OAAOH,GAAG,KAAK,QAAQ,EAAE;QAClC,IAAI,CAACG,MAAM,IAAIH,GAAG;MACpB;MACA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDI,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASE,GAAGA,CAACP,GAAG,EAAE;MACvB,IAAIA,GAAG,YAAYH,aAAa,EAAE;QAChC,IAAI,CAACM,MAAM,IAAIH,GAAG,CAACG,MAAM;MAC3B,CAAC,MAAM,IAAI,OAAOH,GAAG,KAAK,QAAQ,EAAE;QAClC,IAAI,CAACG,MAAM,IAAIH,GAAG;MACpB;MACA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDI,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASG,GAAGA,CAACR,GAAG,EAAE;MACvB,IAAIA,GAAG,YAAYH,aAAa,EAAE;QAChC,IAAI,CAACM,MAAM,IAAIH,GAAG,CAACG,MAAM;MAC3B,CAAC,MAAM,IAAI,OAAOH,GAAG,KAAK,QAAQ,EAAE;QAClC,IAAI,CAACG,MAAM,IAAIH,GAAG;MACpB;MACA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDI,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASI,GAAGA,CAACT,GAAG,EAAE;MACvB,IAAIA,GAAG,YAAYH,aAAa,EAAE;QAChC,IAAI,CAACM,MAAM,IAAIH,GAAG,CAACG,MAAM;MAC3B,CAAC,MAAM,IAAI,OAAOH,GAAG,KAAK,QAAQ,EAAE;QAClC,IAAI,CAACG,MAAM,IAAIH,GAAG;MACpB;MACA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDI,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,SAASK,KAAKA,CAAA,EAAG;MACtB,OAAO,IAAI,CAACP,MAAM;IACpB;EACF,CAAC,CAAC,CAAC;EACH,OAAON,aAAa;AACtB,CAAC,CAACD,kBAAkB,CAAC;AACrB,eAAeC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}