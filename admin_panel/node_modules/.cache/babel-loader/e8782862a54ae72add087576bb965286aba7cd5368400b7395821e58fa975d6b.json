{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport RubyOutlinedSvg from \"@ant-design/icons-svg/es/asn/RubyOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar RubyOutlined = function RubyOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: RubyOutlinedSvg\n  }));\n};\n\n/**![ruby](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNTA5LjgxIDExMi4wMmMtLjczLjA1LTEuNDYuMTItMi4yLjIxaC00LjMybC0zLjQgMS43YTM2LjMzIDM2LjMzIDAgMDAtOC44OCA0LjRsLTE0NS45NiA3My4wMi0xNTMuNyAxNTMuNy03Mi42NSAxNDUuMjRhMzYuMzMgMzYuMzMgMCAwMC00LjkgOS44NmwtMS41NiAzLjEydjMuOThhMzYuMzMgMzYuMzMgMCAwMDAgOC4zdjI5OC4yM2w2Ljg4IDkuNWExOTguNyAxOTguNyAwIDAwMjAuNTggMjQuNDJjMzcuODYgMzcuODUgODcuNjYgNTcuMTYgMTQyLjYyIDYyLjAxYTM2LjM0IDM2LjM0IDAgMDAxMS41NyAxLjc3aDU3NS43NWMzLjE0LjU0IDYuMzQuNjYgOS41MS4zNmEzNi4zNCAzNi4zNCAwIDAwMi41Ni0uMzVoMjkuOHYtMjkuOTVhMzYuMzMgMzYuMzMgMCAwMDAtMTEuOTJWMjkzLjg4YTM2LjMzIDM2LjMzIDAgMDAtMS43OC0xMS41N2MtNC44NC01NC45NS0yNC4xNi0xMDQuNzUtNjIuMDEtMTQyLjYyaC0uMDd2LS4wN2EyMDMuOTIgMjAzLjkyIDAgMDAtMjQuMjctMjAuNDNsLTkuNTgtNi45Nkg1MTUuMTRhMzYuMzQgMzYuMzQgMCAwMC01LjMyLS4yMU02NDMgMTg0Ljg5aDE0NS45NmMyLjQ3IDIuMDggNS4yNSA0LjA2IDcuNDUgNi4yNSAyNi41OSAyNi42MyA0MC45NyA2NC43NCA0Mi4zIDExMS4xOHpNNTEwLjMxIDE5MGw2NS43MSAzOS4zOC0yNS40NyAxNTYuMS02NC4zNiA2NC4zNi0xMDAuNyAxMDAuNjlMMjI5LjQgNTc2bC0zOS4zOC02NS43IDYxLjEtMTIyLjI2IDEzNi45NC0xMzYuOTV6bTEzMi43NiA3OS42MWwxMjMuMTkgNzMuOTQtMTM4LjA5IDE3LjI0ek04MjEuOSA0MDkuODJjLTIxLjIxIDY4LjI1LTYyLjY2IDE0Mi41OC0xMjIuNCAyMTEuODhsLTY1Ljg1LTE4OC40em0tMjUyLjU0IDU5LjZsNTMuNjQgMTUzLjU2LTE1My41NS01My42NSA2OC4xMi02OC4xMnptMjY5LjUgODEuMDR2MjM3TDczOC40NCA2ODcuMDRjNDAuMS00My43NCA3My43My04OS44MyAxMDAuNC0xMzYuNTltLTQ3OC4wNCA3Ny43bC0xNy4yNCAxMzguMDgtNzMuOTQtMTIzLjE4em03Mi41MiA1LjQ2bDE4OC4zMiA2NS44NWMtNjkuMjggNTkuNzEtMTQzLjU3IDEwMS4yLTIxMS44IDEyMi40ek0xODQuOSA2NDNsMTE3LjQzIDE5NS43Yy00Ni41LTEuMzMtODQuNjMtMTUuNzQtMTExLjI2LTQyLjM3LTIuMTYtMi4xNi00LjExLTQuOTMtNi4xNy03LjM4em01MDIuMTcgOTUuNDNsMTAwLjQgMTAwLjRoLTIzN2M0Ni43Ny0yNi42NyA5Mi44Ni02MC4zIDEzNi42LTEwMC40IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(RubyOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'RubyOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "RubyOutlinedSvg", "AntdIcon", "RubyOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/antd/node_modules/@ant-design/icons/es/icons/RubyOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport RubyOutlinedSvg from \"@ant-design/icons-svg/es/asn/RubyOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar RubyOutlined = function RubyOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: RubyOutlinedSvg\n  }));\n};\n\n/**![ruby](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNTA5LjgxIDExMi4wMmMtLjczLjA1LTEuNDYuMTItMi4yLjIxaC00LjMybC0zLjQgMS43YTM2LjMzIDM2LjMzIDAgMDAtOC44OCA0LjRsLTE0NS45NiA3My4wMi0xNTMuNyAxNTMuNy03Mi42NSAxNDUuMjRhMzYuMzMgMzYuMzMgMCAwMC00LjkgOS44NmwtMS41NiAzLjEydjMuOThhMzYuMzMgMzYuMzMgMCAwMDAgOC4zdjI5OC4yM2w2Ljg4IDkuNWExOTguNyAxOTguNyAwIDAwMjAuNTggMjQuNDJjMzcuODYgMzcuODUgODcuNjYgNTcuMTYgMTQyLjYyIDYyLjAxYTM2LjM0IDM2LjM0IDAgMDAxMS41NyAxLjc3aDU3NS43NWMzLjE0LjU0IDYuMzQuNjYgOS41MS4zNmEzNi4zNCAzNi4zNCAwIDAwMi41Ni0uMzVoMjkuOHYtMjkuOTVhMzYuMzMgMzYuMzMgMCAwMDAtMTEuOTJWMjkzLjg4YTM2LjMzIDM2LjMzIDAgMDAtMS43OC0xMS41N2MtNC44NC01NC45NS0yNC4xNi0xMDQuNzUtNjIuMDEtMTQyLjYyaC0uMDd2LS4wN2EyMDMuOTIgMjAzLjkyIDAgMDAtMjQuMjctMjAuNDNsLTkuNTgtNi45Nkg1MTUuMTRhMzYuMzQgMzYuMzQgMCAwMC01LjMyLS4yMU02NDMgMTg0Ljg5aDE0NS45NmMyLjQ3IDIuMDggNS4yNSA0LjA2IDcuNDUgNi4yNSAyNi41OSAyNi42MyA0MC45NyA2NC43NCA0Mi4zIDExMS4xOHpNNTEwLjMxIDE5MGw2NS43MSAzOS4zOC0yNS40NyAxNTYuMS02NC4zNiA2NC4zNi0xMDAuNyAxMDAuNjlMMjI5LjQgNTc2bC0zOS4zOC02NS43IDYxLjEtMTIyLjI2IDEzNi45NC0xMzYuOTV6bTEzMi43NiA3OS42MWwxMjMuMTkgNzMuOTQtMTM4LjA5IDE3LjI0ek04MjEuOSA0MDkuODJjLTIxLjIxIDY4LjI1LTYyLjY2IDE0Mi41OC0xMjIuNCAyMTEuODhsLTY1Ljg1LTE4OC40em0tMjUyLjU0IDU5LjZsNTMuNjQgMTUzLjU2LTE1My41NS01My42NSA2OC4xMi02OC4xMnptMjY5LjUgODEuMDR2MjM3TDczOC40NCA2ODcuMDRjNDAuMS00My43NCA3My43My04OS44MyAxMDAuNC0xMzYuNTltLTQ3OC4wNCA3Ny43bC0xNy4yNCAxMzguMDgtNzMuOTQtMTIzLjE4em03Mi41MiA1LjQ2bDE4OC4zMiA2NS44NWMtNjkuMjggNTkuNzEtMTQzLjU3IDEwMS4yLTIxMS44IDEyMi40ek0xODQuOSA2NDNsMTE3LjQzIDE5NS43Yy00Ni41LTEuMzMtODQuNjMtMTUuNzQtMTExLjI2LTQyLjM3LTIuMTYtMi4xNi00LjExLTQuOTMtNi4xNy03LjM4em01MDIuMTcgOTUuNDNsMTAwLjQgMTAwLjRoLTIzN2M0Ni43Ny0yNi42NyA5Mi44Ni02MC4zIDEzNi42LTEwMC40IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(RubyOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'RubyOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACnD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,YAAY,CAAC;AACzD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,cAAc;AACtC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}