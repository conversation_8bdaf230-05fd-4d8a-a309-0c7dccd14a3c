{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nfunction parseColWidth(totalWidth) {\n  var width = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n  if (typeof width === 'number') {\n    return width;\n  }\n  if (width.endsWith('%')) {\n    return totalWidth * parseFloat(width) / 100;\n  }\n  return null;\n}\n\n/**\n * Fill all column with width\n */\nexport default function useWidthColumns(flattenColumns, scrollWidth, clientWidth) {\n  return React.useMemo(function () {\n    // Fill width if needed\n    if (scrollWidth && scrollWidth > 0) {\n      var totalWidth = 0;\n      var missWidthCount = 0;\n\n      // collect not given width column\n      flattenColumns.forEach(function (col) {\n        var colWidth = parseColWidth(scrollWidth, col.width);\n        if (colWidth) {\n          totalWidth += colWidth;\n        } else {\n          missWidthCount += 1;\n        }\n      });\n\n      // Fill width\n      var maxFitWidth = Math.max(scrollWidth, clientWidth);\n      var restWidth = Math.max(maxFitWidth - totalWidth, missWidthCount);\n      var restCount = missWidthCount;\n      var avgWidth = restWidth / missWidthCount;\n      var realTotal = 0;\n      var filledColumns = flattenColumns.map(function (col) {\n        var clone = _objectSpread({}, col);\n        var colWidth = parseColWidth(scrollWidth, clone.width);\n        if (colWidth) {\n          clone.width = colWidth;\n        } else {\n          var colAvgWidth = Math.floor(avgWidth);\n          clone.width = restCount === 1 ? restWidth : colAvgWidth;\n          restWidth -= colAvgWidth;\n          restCount -= 1;\n        }\n        realTotal += clone.width;\n        return clone;\n      });\n\n      // If realTotal is less than clientWidth,\n      // We need extend column width\n      if (realTotal < maxFitWidth) {\n        var scale = maxFitWidth / realTotal;\n        restWidth = maxFitWidth;\n        filledColumns.forEach(function (col, index) {\n          var colWidth = Math.floor(col.width * scale);\n          col.width = index === filledColumns.length - 1 ? restWidth : colWidth;\n          restWidth -= colWidth;\n        });\n      }\n      return [filledColumns, Math.max(realTotal, maxFitWidth)];\n    }\n    return [flattenColumns, scrollWidth];\n  }, [flattenColumns, scrollWidth, clientWidth]);\n}", "map": {"version": 3, "names": ["_objectSpread", "React", "parseCol<PERSON><PERSON>th", "totalWidth", "width", "arguments", "length", "undefined", "endsWith", "parseFloat", "useWidthColumns", "flattenColumns", "scrollWidth", "clientWidth", "useMemo", "miss<PERSON><PERSON><PERSON><PERSON>ount", "for<PERSON>ach", "col", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "maxFit<PERSON>idth", "Math", "max", "restWidth", "restCount", "avgWidth", "realTotal", "filledColumns", "map", "clone", "colAvgWidth", "floor", "scale", "index"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/rc-table/es/hooks/useColumns/useWidthColumns.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nfunction parseColWidth(totalWidth) {\n  var width = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n  if (typeof width === 'number') {\n    return width;\n  }\n  if (width.endsWith('%')) {\n    return totalWidth * parseFloat(width) / 100;\n  }\n  return null;\n}\n\n/**\n * Fill all column with width\n */\nexport default function useWidthColumns(flattenColumns, scrollWidth, clientWidth) {\n  return React.useMemo(function () {\n    // Fill width if needed\n    if (scrollWidth && scrollWidth > 0) {\n      var totalWidth = 0;\n      var missWidthCount = 0;\n\n      // collect not given width column\n      flattenColumns.forEach(function (col) {\n        var colWidth = parseColWidth(scrollWidth, col.width);\n        if (colWidth) {\n          totalWidth += colWidth;\n        } else {\n          missWidthCount += 1;\n        }\n      });\n\n      // Fill width\n      var maxFitWidth = Math.max(scrollWidth, clientWidth);\n      var restWidth = Math.max(maxFitWidth - totalWidth, missWidthCount);\n      var restCount = missWidthCount;\n      var avgWidth = restWidth / missWidthCount;\n      var realTotal = 0;\n      var filledColumns = flattenColumns.map(function (col) {\n        var clone = _objectSpread({}, col);\n        var colWidth = parseColWidth(scrollWidth, clone.width);\n        if (colWidth) {\n          clone.width = colWidth;\n        } else {\n          var colAvgWidth = Math.floor(avgWidth);\n          clone.width = restCount === 1 ? restWidth : colAvgWidth;\n          restWidth -= colAvgWidth;\n          restCount -= 1;\n        }\n        realTotal += clone.width;\n        return clone;\n      });\n\n      // If realTotal is less than clientWidth,\n      // We need extend column width\n      if (realTotal < maxFitWidth) {\n        var scale = maxFitWidth / realTotal;\n        restWidth = maxFitWidth;\n        filledColumns.forEach(function (col, index) {\n          var colWidth = Math.floor(col.width * scale);\n          col.width = index === filledColumns.length - 1 ? restWidth : colWidth;\n          restWidth -= colWidth;\n        });\n      }\n      return [filledColumns, Math.max(realTotal, maxFitWidth)];\n    }\n    return [flattenColumns, scrollWidth];\n  }, [flattenColumns, scrollWidth, clientWidth]);\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAaA,CAACC,UAAU,EAAE;EACjC,IAAIC,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EAClF,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOA,KAAK;EACd;EACA,IAAIA,KAAK,CAACI,QAAQ,CAAC,GAAG,CAAC,EAAE;IACvB,OAAOL,UAAU,GAAGM,UAAU,CAACL,KAAK,CAAC,GAAG,GAAG;EAC7C;EACA,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA,eAAe,SAASM,eAAeA,CAACC,cAAc,EAAEC,WAAW,EAAEC,WAAW,EAAE;EAChF,OAAOZ,KAAK,CAACa,OAAO,CAAC,YAAY;IAC/B;IACA,IAAIF,WAAW,IAAIA,WAAW,GAAG,CAAC,EAAE;MAClC,IAAIT,UAAU,GAAG,CAAC;MAClB,IAAIY,cAAc,GAAG,CAAC;;MAEtB;MACAJ,cAAc,CAACK,OAAO,CAAC,UAAUC,GAAG,EAAE;QACpC,IAAIC,QAAQ,GAAGhB,aAAa,CAACU,WAAW,EAAEK,GAAG,CAACb,KAAK,CAAC;QACpD,IAAIc,QAAQ,EAAE;UACZf,UAAU,IAAIe,QAAQ;QACxB,CAAC,MAAM;UACLH,cAAc,IAAI,CAAC;QACrB;MACF,CAAC,CAAC;;MAEF;MACA,IAAII,WAAW,GAAGC,IAAI,CAACC,GAAG,CAACT,WAAW,EAAEC,WAAW,CAAC;MACpD,IAAIS,SAAS,GAAGF,IAAI,CAACC,GAAG,CAACF,WAAW,GAAGhB,UAAU,EAAEY,cAAc,CAAC;MAClE,IAAIQ,SAAS,GAAGR,cAAc;MAC9B,IAAIS,QAAQ,GAAGF,SAAS,GAAGP,cAAc;MACzC,IAAIU,SAAS,GAAG,CAAC;MACjB,IAAIC,aAAa,GAAGf,cAAc,CAACgB,GAAG,CAAC,UAAUV,GAAG,EAAE;QACpD,IAAIW,KAAK,GAAG5B,aAAa,CAAC,CAAC,CAAC,EAAEiB,GAAG,CAAC;QAClC,IAAIC,QAAQ,GAAGhB,aAAa,CAACU,WAAW,EAAEgB,KAAK,CAACxB,KAAK,CAAC;QACtD,IAAIc,QAAQ,EAAE;UACZU,KAAK,CAACxB,KAAK,GAAGc,QAAQ;QACxB,CAAC,MAAM;UACL,IAAIW,WAAW,GAAGT,IAAI,CAACU,KAAK,CAACN,QAAQ,CAAC;UACtCI,KAAK,CAACxB,KAAK,GAAGmB,SAAS,KAAK,CAAC,GAAGD,SAAS,GAAGO,WAAW;UACvDP,SAAS,IAAIO,WAAW;UACxBN,SAAS,IAAI,CAAC;QAChB;QACAE,SAAS,IAAIG,KAAK,CAACxB,KAAK;QACxB,OAAOwB,KAAK;MACd,CAAC,CAAC;;MAEF;MACA;MACA,IAAIH,SAAS,GAAGN,WAAW,EAAE;QAC3B,IAAIY,KAAK,GAAGZ,WAAW,GAAGM,SAAS;QACnCH,SAAS,GAAGH,WAAW;QACvBO,aAAa,CAACV,OAAO,CAAC,UAAUC,GAAG,EAAEe,KAAK,EAAE;UAC1C,IAAId,QAAQ,GAAGE,IAAI,CAACU,KAAK,CAACb,GAAG,CAACb,KAAK,GAAG2B,KAAK,CAAC;UAC5Cd,GAAG,CAACb,KAAK,GAAG4B,KAAK,KAAKN,aAAa,CAACpB,MAAM,GAAG,CAAC,GAAGgB,SAAS,GAAGJ,QAAQ;UACrEI,SAAS,IAAIJ,QAAQ;QACvB,CAAC,CAAC;MACJ;MACA,OAAO,CAACQ,aAAa,EAAEN,IAAI,CAACC,GAAG,CAACI,SAAS,EAAEN,WAAW,CAAC,CAAC;IAC1D;IACA,OAAO,CAACR,cAAc,EAAEC,WAAW,CAAC;EACtC,CAAC,EAAE,CAACD,cAAc,EAAEC,WAAW,EAAEC,WAAW,CAAC,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}