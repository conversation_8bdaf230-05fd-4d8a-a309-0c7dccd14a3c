{"ast": null, "code": "/* istanbul ignore file */\nexport function isPlatformMac() {\n  return /(mac\\sos|macintosh)/i.test(navigator.appVersion);\n}", "map": {"version": 3, "names": ["isPlatformMac", "test", "navigator", "appVersion"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/rc-select/es/utils/platformUtil.js"], "sourcesContent": ["/* istanbul ignore file */\nexport function isPlatformMac() {\n  return /(mac\\sos|macintosh)/i.test(navigator.appVersion);\n}"], "mappings": "AAAA;AACA,OAAO,SAASA,aAAaA,CAAA,EAAG;EAC9B,OAAO,sBAAsB,CAACC,IAAI,CAACC,SAAS,CAACC,UAAU,CAAC;AAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}