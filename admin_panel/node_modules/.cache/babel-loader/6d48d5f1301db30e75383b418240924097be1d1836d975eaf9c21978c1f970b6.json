{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nvar ExtraContent = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var position = props.position,\n    prefixCls = props.prefixCls,\n    extra = props.extra;\n  if (!extra) {\n    return null;\n  }\n  var content;\n\n  // Parse extra\n  var assertExtra = {};\n  if (_typeof(extra) === 'object' && ! /*#__PURE__*/React.isValidElement(extra)) {\n    assertExtra = extra;\n  } else {\n    assertExtra.right = extra;\n  }\n  if (position === 'right') {\n    content = assertExtra.right;\n  }\n  if (position === 'left') {\n    content = assertExtra.left;\n  }\n  return content ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-extra-content\"),\n    ref: ref\n  }, content) : null;\n});\nif (process.env.NODE_ENV !== 'production') {\n  ExtraContent.displayName = 'ExtraContent';\n}\nexport default ExtraContent;", "map": {"version": 3, "names": ["_typeof", "React", "ExtraContent", "forwardRef", "props", "ref", "position", "prefixCls", "extra", "content", "assertExtra", "isValidElement", "right", "left", "createElement", "className", "concat", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/rc-tabs/es/TabNavList/ExtraContent.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nvar ExtraContent = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var position = props.position,\n    prefixCls = props.prefixCls,\n    extra = props.extra;\n  if (!extra) {\n    return null;\n  }\n  var content;\n\n  // Parse extra\n  var assertExtra = {};\n  if (_typeof(extra) === 'object' && ! /*#__PURE__*/React.isValidElement(extra)) {\n    assertExtra = extra;\n  } else {\n    assertExtra.right = extra;\n  }\n  if (position === 'right') {\n    content = assertExtra.right;\n  }\n  if (position === 'left') {\n    content = assertExtra.left;\n  }\n  return content ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-extra-content\"),\n    ref: ref\n  }, content) : null;\n});\nif (process.env.NODE_ENV !== 'production') {\n  ExtraContent.displayName = 'ExtraContent';\n}\nexport default ExtraContent;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,YAAY,GAAG,aAAaD,KAAK,CAACE,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACrE,IAAIC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IAC3BC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,KAAK,GAAGJ,KAAK,CAACI,KAAK;EACrB,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EACA,IAAIC,OAAO;;EAEX;EACA,IAAIC,WAAW,GAAG,CAAC,CAAC;EACpB,IAAIV,OAAO,CAACQ,KAAK,CAAC,KAAK,QAAQ,IAAI,EAAE,aAAaP,KAAK,CAACU,cAAc,CAACH,KAAK,CAAC,EAAE;IAC7EE,WAAW,GAAGF,KAAK;EACrB,CAAC,MAAM;IACLE,WAAW,CAACE,KAAK,GAAGJ,KAAK;EAC3B;EACA,IAAIF,QAAQ,KAAK,OAAO,EAAE;IACxBG,OAAO,GAAGC,WAAW,CAACE,KAAK;EAC7B;EACA,IAAIN,QAAQ,KAAK,MAAM,EAAE;IACvBG,OAAO,GAAGC,WAAW,CAACG,IAAI;EAC5B;EACA,OAAOJ,OAAO,GAAG,aAAaR,KAAK,CAACa,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACT,SAAS,EAAE,gBAAgB,CAAC;IACjDF,GAAG,EAAEA;EACP,CAAC,EAAEI,OAAO,CAAC,GAAG,IAAI;AACpB,CAAC,CAAC;AACF,IAAIQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCjB,YAAY,CAACkB,WAAW,GAAG,cAAc;AAC3C;AACA,eAAelB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}