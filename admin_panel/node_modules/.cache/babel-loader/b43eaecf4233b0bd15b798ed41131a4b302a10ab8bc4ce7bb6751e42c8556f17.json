{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport UnderlineOutlinedSvg from \"@ant-design/icons-svg/es/asn/UnderlineOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar UnderlineOutlined = function UnderlineOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: UnderlineOutlinedSvg\n  }));\n};\n\n/**![underline](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgyNCA4MDRIMjAwYy00LjQgMC04IDMuNC04IDcuNnY2MC44YzAgNC4yIDMuNiA3LjYgOCA3LjZoNjI0YzQuNCAwIDgtMy40IDgtNy42di02MC44YzAtNC4yLTMuNi03LjYtOC03LjZ6bS0zMTItNzZjNjkuNCAwIDEzNC42LTI3LjEgMTgzLjgtNzYuMkM3NDUgNjAyLjcgNzcyIDUzNy40IDc3MiA0NjhWMTU2YzAtNi42LTUuNC0xMi0xMi0xMmgtNjBjLTYuNiAwLTEyIDUuNC0xMiAxMnYzMTJjMCA5Ny03OSAxNzYtMTc2IDE3NnMtMTc2LTc5LTE3Ni0xNzZWMTU2YzAtNi42LTUuNC0xMi0xMi0xMmgtNjBjLTYuNiAwLTEyIDUuNC0xMiAxMnYzMTJjMCA2OS40IDI3LjEgMTM0LjYgNzYuMiAxODMuOEMzNzcuMyA3MDEgNDQyLjYgNzI4IDUxMiA3Mjh6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(UnderlineOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'UnderlineOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "UnderlineOutlinedSvg", "AntdIcon", "UnderlineOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/antd/node_modules/@ant-design/icons/es/icons/UnderlineOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport UnderlineOutlinedSvg from \"@ant-design/icons-svg/es/asn/UnderlineOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar UnderlineOutlined = function UnderlineOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: UnderlineOutlinedSvg\n  }));\n};\n\n/**![underline](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgyNCA4MDRIMjAwYy00LjQgMC04IDMuNC04IDcuNnY2MC44YzAgNC4yIDMuNiA3LjYgOCA3LjZoNjI0YzQuNCAwIDgtMy40IDgtNy42di02MC44YzAtNC4yLTMuNi03LjYtOC03LjZ6bS0zMTItNzZjNjkuNCAwIDEzNC42LTI3LjEgMTgzLjgtNzYuMkM3NDUgNjAyLjcgNzcyIDUzNy40IDc3MiA0NjhWMTU2YzAtNi42LTUuNC0xMi0xMi0xMmgtNjBjLTYuNiAwLTEyIDUuNC0xMiAxMnYzMTJjMCA5Ny03OSAxNzYtMTc2IDE3NnMtMTc2LTc5LTE3Ni0xNzZWMTU2YzAtNi42LTUuNC0xMi0xMi0xMmgtNjBjLTYuNiAwLTEyIDUuNC0xMiAxMnYzMTJjMCA2OS40IDI3LjEgMTM0LjYgNzYuMiAxODMuOEMzNzcuMyA3MDEgNDQyLjYgNzI4IDUxMiA3Mjh6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(UnderlineOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'UnderlineOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,oBAAoB,MAAM,gDAAgD;AACjF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC7D,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,iBAAiB,CAAC;AAC9D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,mBAAmB;AAC3C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}