{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genStickyStyle = token => {\n  const {\n    componentCls,\n    opacityLoading,\n    tableScrollThumbBg,\n    tableScrollThumbBgHover,\n    tableScrollThumbSize,\n    tableScrollBg,\n    zIndexTableSticky,\n    stickyScrollBarBorderRadius,\n    lineWidth,\n    lineType,\n    tableBorderColor\n  } = token;\n  const tableBorder = `${unit(lineWidth)} ${lineType} ${tableBorderColor}`;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-sticky`]: {\n        '&-holder': {\n          position: 'sticky',\n          zIndex: zIndexTableSticky,\n          background: token.colorBgContainer\n        },\n        '&-scroll': {\n          position: 'sticky',\n          bottom: 0,\n          height: `${unit(tableScrollThumbSize)} !important`,\n          zIndex: zIndexTableSticky,\n          display: 'flex',\n          alignItems: 'center',\n          background: tableScrollBg,\n          borderTop: tableBorder,\n          opacity: opacityLoading,\n          '&:hover': {\n            transformOrigin: 'center bottom'\n          },\n          // fake scrollbar style of sticky\n          '&-bar': {\n            height: tableScrollThumbSize,\n            backgroundColor: tableScrollThumbBg,\n            borderRadius: stickyScrollBarBorderRadius,\n            transition: `all ${token.motionDurationSlow}, transform 0s`,\n            position: 'absolute',\n            bottom: 0,\n            '&:hover, &-active': {\n              backgroundColor: tableScrollThumbBgHover\n            }\n          }\n        }\n      }\n    }\n  };\n};\nexport default genStickyStyle;", "map": {"version": 3, "names": ["unit", "genStickyStyle", "token", "componentCls", "opacityLoading", "tableScrollThumbBg", "tableScrollThumbBgHover", "tableScrollThumbSize", "tableScrollBg", "zIndexTableSticky", "stickyScrollBarBorderRadius", "lineWidth", "lineType", "tableBorderColor", "tableBorder", "position", "zIndex", "background", "colorBgContainer", "bottom", "height", "display", "alignItems", "borderTop", "opacity", "transform<PERSON><PERSON>in", "backgroundColor", "borderRadius", "transition", "motionDurationSlow"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/antd/es/table/style/sticky.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst genStickyStyle = token => {\n  const {\n    componentCls,\n    opacityLoading,\n    tableScrollThumbBg,\n    tableScrollThumbBgHover,\n    tableScrollThumbSize,\n    tableScrollBg,\n    zIndexTableSticky,\n    stickyScrollBarBorderRadius,\n    lineWidth,\n    lineType,\n    tableBorderColor\n  } = token;\n  const tableBorder = `${unit(lineWidth)} ${lineType} ${tableBorderColor}`;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-sticky`]: {\n        '&-holder': {\n          position: 'sticky',\n          zIndex: zIndexTableSticky,\n          background: token.colorBgContainer\n        },\n        '&-scroll': {\n          position: 'sticky',\n          bottom: 0,\n          height: `${unit(tableScrollThumbSize)} !important`,\n          zIndex: zIndexTableSticky,\n          display: 'flex',\n          alignItems: 'center',\n          background: tableScrollBg,\n          borderTop: tableBorder,\n          opacity: opacityLoading,\n          '&:hover': {\n            transformOrigin: 'center bottom'\n          },\n          // fake scrollbar style of sticky\n          '&-bar': {\n            height: tableScrollThumbSize,\n            backgroundColor: tableScrollThumbBg,\n            borderRadius: stickyScrollBarBorderRadius,\n            transition: `all ${token.motionDurationSlow}, transform 0s`,\n            position: 'absolute',\n            bottom: 0,\n            '&:hover, &-active': {\n              backgroundColor: tableScrollThumbBgHover\n            }\n          }\n        }\n      }\n    }\n  };\n};\nexport default genStickyStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,MAAMC,cAAc,GAAGC,KAAK,IAAI;EAC9B,MAAM;IACJC,YAAY;IACZC,cAAc;IACdC,kBAAkB;IAClBC,uBAAuB;IACvBC,oBAAoB;IACpBC,aAAa;IACbC,iBAAiB;IACjBC,2BAA2B;IAC3BC,SAAS;IACTC,QAAQ;IACRC;EACF,CAAC,GAAGX,KAAK;EACT,MAAMY,WAAW,GAAG,GAAGd,IAAI,CAACW,SAAS,CAAC,IAAIC,QAAQ,IAAIC,gBAAgB,EAAE;EACxE,OAAO;IACL,CAAC,GAAGV,YAAY,UAAU,GAAG;MAC3B,CAAC,GAAGA,YAAY,SAAS,GAAG;QAC1B,UAAU,EAAE;UACVY,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAEP,iBAAiB;UACzBQ,UAAU,EAAEf,KAAK,CAACgB;QACpB,CAAC;QACD,UAAU,EAAE;UACVH,QAAQ,EAAE,QAAQ;UAClBI,MAAM,EAAE,CAAC;UACTC,MAAM,EAAE,GAAGpB,IAAI,CAACO,oBAAoB,CAAC,aAAa;UAClDS,MAAM,EAAEP,iBAAiB;UACzBY,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBL,UAAU,EAAET,aAAa;UACzBe,SAAS,EAAET,WAAW;UACtBU,OAAO,EAAEpB,cAAc;UACvB,SAAS,EAAE;YACTqB,eAAe,EAAE;UACnB,CAAC;UACD;UACA,OAAO,EAAE;YACPL,MAAM,EAAEb,oBAAoB;YAC5BmB,eAAe,EAAErB,kBAAkB;YACnCsB,YAAY,EAAEjB,2BAA2B;YACzCkB,UAAU,EAAE,OAAO1B,KAAK,CAAC2B,kBAAkB,gBAAgB;YAC3Dd,QAAQ,EAAE,UAAU;YACpBI,MAAM,EAAE,CAAC;YACT,mBAAmB,EAAE;cACnBO,eAAe,EAAEpB;YACnB;UACF;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeL,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}