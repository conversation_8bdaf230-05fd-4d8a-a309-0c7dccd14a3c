{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport StarTwoToneSvg from \"@ant-design/icons-svg/es/asn/StarTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar StarTwoTone = function StarTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: StarTwoToneSvg\n  }));\n};\n\n/**![star](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMi41IDE5MC40bC05NC40IDE5MS4zLTIxMS4yIDMwLjcgMTUyLjggMTQ5LTM2LjEgMjEwLjMgMTg4LjktOTkuMyAxODguOSA5OS4yLTM2LjEtMjEwLjMgMTUyLjgtMTQ4LjktMjExLjItMzAuN3oiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTkwOC42IDM1Mi44bC0yNTMuOS0zNi45TDU0MS4yIDg1LjhjLTMuMS02LjMtOC4yLTExLjQtMTQuNS0xNC41LTE1LjgtNy44LTM1LTEuMy00Mi45IDE0LjVMMzcwLjMgMzE1LjlsLTI1My45IDM2LjljLTcgMS0xMy40IDQuMy0xOC4zIDkuM2EzMi4wNSAzMi4wNSAwIDAwLjYgNDUuM2wxODMuNyAxNzkuMUwyMzkgODM5LjRhMzEuOTUgMzEuOTUgMCAwMDQ2LjQgMzMuN2wyMjcuMS0xMTkuNCAyMjcuMSAxMTkuNGM2LjIgMy4zIDEzLjQgNC40IDIwLjMgMy4yIDE3LjQtMyAyOS4xLTE5LjUgMjYuMS0zNi45bC00My40LTI1Mi45IDE4My43LTE3OS4xYzUtNC45IDguMy0xMS4zIDkuMy0xOC4zIDIuNy0xNy41LTkuNS0zMy43LTI3LTM2LjN6TTY2NS4zIDU2MS4zbDM2LjEgMjEwLjMtMTg4LjktOTkuMi0xODguOSA5OS4zIDM2LjEtMjEwLjMtMTUyLjgtMTQ5IDIxMS4yLTMwLjcgOTQuNC0xOTEuMyA5NC40IDE5MS4zIDIxMS4yIDMwLjctMTUyLjggMTQ4Ljl6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(StarTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'StarTwoTone';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "StarTwoToneSvg", "AntdIcon", "StarTwoTone", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/antd/node_modules/@ant-design/icons/es/icons/StarTwoTone.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport StarTwoToneSvg from \"@ant-design/icons-svg/es/asn/StarTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar StarTwoTone = function StarTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: StarTwoToneSvg\n  }));\n};\n\n/**![star](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMi41IDE5MC40bC05NC40IDE5MS4zLTIxMS4yIDMwLjcgMTUyLjggMTQ5LTM2LjEgMjEwLjMgMTg4LjktOTkuMyAxODguOSA5OS4yLTM2LjEtMjEwLjMgMTUyLjgtMTQ4LjktMjExLjItMzAuN3oiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTkwOC42IDM1Mi44bC0yNTMuOS0zNi45TDU0MS4yIDg1LjhjLTMuMS02LjMtOC4yLTExLjQtMTQuNS0xNC41LTE1LjgtNy44LTM1LTEuMy00Mi45IDE0LjVMMzcwLjMgMzE1LjlsLTI1My45IDM2LjljLTcgMS0xMy40IDQuMy0xOC4zIDkuM2EzMi4wNSAzMi4wNSAwIDAwLjYgNDUuM2wxODMuNyAxNzkuMUwyMzkgODM5LjRhMzEuOTUgMzEuOTUgMCAwMDQ2LjQgMzMuN2wyMjcuMS0xMTkuNCAyMjcuMSAxMTkuNGM2LjIgMy4zIDEzLjQgNC40IDIwLjMgMy4yIDE3LjQtMyAyOS4xLTE5LjUgMjYuMS0zNi45bC00My40LTI1Mi45IDE4My43LTE3OS4xYzUtNC45IDguMy0xMS4zIDkuMy0xOC4zIDIuNy0xNy41LTkuNS0zMy43LTI3LTM2LjN6TTY2NS4zIDU2MS4zbDM2LjEgMjEwLjMtMTg4LjktOTkuMi0xODguOSA5OS4zIDM2LjEtMjEwLjMtMTUyLjgtMTQ5IDIxMS4yLTMwLjcgOTQuNC0xOTEuMyA5NC40IDE5MS4zIDIxMS4yIDMwLjctMTUyLjggMTQ4Ljl6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(StarTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'StarTwoTone';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,WAAW,CAAC;AACxD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,aAAa;AACrC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}