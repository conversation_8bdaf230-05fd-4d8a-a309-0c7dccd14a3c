{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport CascaderContext from \"../context\";\n\n/**\n * Control the active open options path.\n */\nvar useActive = function useActive(multiple, open) {\n  var _React$useContext = React.useContext(CascaderContext),\n    values = _React$useContext.values;\n  var firstValueCells = values[0];\n\n  // Record current dropdown active options\n  // This also control the open status\n  var _React$useState = React.useState([]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeValueCells = _React$useState2[0],\n    setActiveValueCells = _React$useState2[1];\n  React.useEffect(function () {\n    if (!multiple) {\n      setActiveValueCells(firstValueCells || []);\n    }\n  }, /* eslint-disable react-hooks/exhaustive-deps */\n  [open, firstValueCells]\n  /* eslint-enable react-hooks/exhaustive-deps */);\n  return [activeValueCells, setActiveValueCells];\n};\nexport default useActive;", "map": {"version": 3, "names": ["_slicedToArray", "React", "CascaderContext", "useActive", "multiple", "open", "_React$useContext", "useContext", "values", "firstValueCells", "_React$useState", "useState", "_React$useState2", "activeValueCells", "setActiveValueCells", "useEffect"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/rc-cascader/es/OptionList/useActive.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport CascaderContext from \"../context\";\n\n/**\n * Control the active open options path.\n */\nvar useActive = function useActive(multiple, open) {\n  var _React$useContext = React.useContext(CascaderContext),\n    values = _React$useContext.values;\n  var firstValueCells = values[0];\n\n  // Record current dropdown active options\n  // This also control the open status\n  var _React$useState = React.useState([]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeValueCells = _React$useState2[0],\n    setActiveValueCells = _React$useState2[1];\n  React.useEffect(function () {\n    if (!multiple) {\n      setActiveValueCells(firstValueCells || []);\n    }\n  }, /* eslint-disable react-hooks/exhaustive-deps */\n  [open, firstValueCells]\n  /* eslint-enable react-hooks/exhaustive-deps */);\n\n  return [activeValueCells, setActiveValueCells];\n};\nexport default useActive;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,YAAY;;AAExC;AACA;AACA;AACA,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,QAAQ,EAAEC,IAAI,EAAE;EACjD,IAAIC,iBAAiB,GAAGL,KAAK,CAACM,UAAU,CAACL,eAAe,CAAC;IACvDM,MAAM,GAAGF,iBAAiB,CAACE,MAAM;EACnC,IAAIC,eAAe,GAAGD,MAAM,CAAC,CAAC,CAAC;;EAE/B;EACA;EACA,IAAIE,eAAe,GAAGT,KAAK,CAACU,QAAQ,CAAC,EAAE,CAAC;IACtCC,gBAAgB,GAAGZ,cAAc,CAACU,eAAe,EAAE,CAAC,CAAC;IACrDG,gBAAgB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACtCE,mBAAmB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC3CX,KAAK,CAACc,SAAS,CAAC,YAAY;IAC1B,IAAI,CAACX,QAAQ,EAAE;MACbU,mBAAmB,CAACL,eAAe,IAAI,EAAE,CAAC;IAC5C;EACF,CAAC,EAAE;EACH,CAACJ,IAAI,EAAEI,eAAe;EACtB,+CAA+C,CAAC;EAEhD,OAAO,CAACI,gBAAgB,EAAEC,mBAAmB,CAAC;AAChD,CAAC;AACD,eAAeX,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}