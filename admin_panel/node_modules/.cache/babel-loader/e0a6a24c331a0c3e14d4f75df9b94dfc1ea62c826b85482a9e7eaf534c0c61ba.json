{"ast": null, "code": "\"use strict\";\n\nvar deselectCurrent = require(\"toggle-selection\");\nvar clipboardToIE11Formatting = {\n  \"text/plain\": \"Text\",\n  \"text/html\": \"Url\",\n  \"default\": \"Text\"\n};\nvar defaultMessage = \"Copy to clipboard: #{key}, Enter\";\nfunction format(message) {\n  var copyKey = (/mac os x/i.test(navigator.userAgent) ? \"⌘\" : \"Ctrl\") + \"+C\";\n  return message.replace(/#{\\s*key\\s*}/g, copyKey);\n}\nfunction copy(text, options) {\n  var debug,\n    message,\n    reselectPrevious,\n    range,\n    selection,\n    mark,\n    success = false;\n  if (!options) {\n    options = {};\n  }\n  debug = options.debug || false;\n  try {\n    reselectPrevious = deselectCurrent();\n    range = document.createRange();\n    selection = document.getSelection();\n    mark = document.createElement(\"span\");\n    mark.textContent = text;\n    // avoid screen readers from reading out loud the text\n    mark.ariaHidden = \"true\";\n    // reset user styles for span element\n    mark.style.all = \"unset\";\n    // prevents scrolling to the end of the page\n    mark.style.position = \"fixed\";\n    mark.style.top = 0;\n    mark.style.clip = \"rect(0, 0, 0, 0)\";\n    // used to preserve spaces and line breaks\n    mark.style.whiteSpace = \"pre\";\n    // do not inherit user-select (it may be `none`)\n    mark.style.webkitUserSelect = \"text\";\n    mark.style.MozUserSelect = \"text\";\n    mark.style.msUserSelect = \"text\";\n    mark.style.userSelect = \"text\";\n    mark.addEventListener(\"copy\", function (e) {\n      e.stopPropagation();\n      if (options.format) {\n        e.preventDefault();\n        if (typeof e.clipboardData === \"undefined\") {\n          // IE 11\n          debug && console.warn(\"unable to use e.clipboardData\");\n          debug && console.warn(\"trying IE specific stuff\");\n          window.clipboardData.clearData();\n          var format = clipboardToIE11Formatting[options.format] || clipboardToIE11Formatting[\"default\"];\n          window.clipboardData.setData(format, text);\n        } else {\n          // all other browsers\n          e.clipboardData.clearData();\n          e.clipboardData.setData(options.format, text);\n        }\n      }\n      if (options.onCopy) {\n        e.preventDefault();\n        options.onCopy(e.clipboardData);\n      }\n    });\n    document.body.appendChild(mark);\n    range.selectNodeContents(mark);\n    selection.addRange(range);\n    var successful = document.execCommand(\"copy\");\n    if (!successful) {\n      throw new Error(\"copy command was unsuccessful\");\n    }\n    success = true;\n  } catch (err) {\n    debug && console.error(\"unable to copy using execCommand: \", err);\n    debug && console.warn(\"trying IE specific stuff\");\n    try {\n      window.clipboardData.setData(options.format || \"text\", text);\n      options.onCopy && options.onCopy(window.clipboardData);\n      success = true;\n    } catch (err) {\n      debug && console.error(\"unable to copy using clipboardData: \", err);\n      debug && console.error(\"falling back to prompt\");\n      message = format(\"message\" in options ? options.message : defaultMessage);\n      window.prompt(message, text);\n    }\n  } finally {\n    if (selection) {\n      if (typeof selection.removeRange == \"function\") {\n        selection.removeRange(range);\n      } else {\n        selection.removeAllRanges();\n      }\n    }\n    if (mark) {\n      document.body.removeChild(mark);\n    }\n    reselectPrevious();\n  }\n  return success;\n}\nmodule.exports = copy;", "map": {"version": 3, "names": ["deselectCurrent", "require", "clipboardToIE11Formatting", "defaultMessage", "format", "message", "copyKey", "test", "navigator", "userAgent", "replace", "copy", "text", "options", "debug", "reselectPrevious", "range", "selection", "mark", "success", "document", "createRange", "getSelection", "createElement", "textContent", "ariaHidden", "style", "all", "position", "top", "clip", "whiteSpace", "webkitUserSelect", "MozUserSelect", "msUserSelect", "userSelect", "addEventListener", "e", "stopPropagation", "preventDefault", "clipboardData", "console", "warn", "window", "clearData", "setData", "onCopy", "body", "append<PERSON><PERSON><PERSON>", "selectNodeContents", "addRange", "successful", "execCommand", "Error", "err", "error", "prompt", "<PERSON><PERSON><PERSON><PERSON>", "removeAllRanges", "<PERSON><PERSON><PERSON><PERSON>", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/copy-to-clipboard/index.js"], "sourcesContent": ["\"use strict\";\n\nvar deselectCurrent = require(\"toggle-selection\");\n\nvar clipboardToIE11Formatting = {\n  \"text/plain\": \"Text\",\n  \"text/html\": \"Url\",\n  \"default\": \"Text\"\n}\n\nvar defaultMessage = \"Copy to clipboard: #{key}, Enter\";\n\nfunction format(message) {\n  var copyKey = (/mac os x/i.test(navigator.userAgent) ? \"⌘\" : \"Ctrl\") + \"+C\";\n  return message.replace(/#{\\s*key\\s*}/g, copyKey);\n}\n\nfunction copy(text, options) {\n  var debug,\n    message,\n    reselectPrevious,\n    range,\n    selection,\n    mark,\n    success = false;\n  if (!options) {\n    options = {};\n  }\n  debug = options.debug || false;\n  try {\n    reselectPrevious = deselectCurrent();\n\n    range = document.createRange();\n    selection = document.getSelection();\n\n    mark = document.createElement(\"span\");\n    mark.textContent = text;\n    // avoid screen readers from reading out loud the text\n    mark.ariaHidden = \"true\"\n    // reset user styles for span element\n    mark.style.all = \"unset\";\n    // prevents scrolling to the end of the page\n    mark.style.position = \"fixed\";\n    mark.style.top = 0;\n    mark.style.clip = \"rect(0, 0, 0, 0)\";\n    // used to preserve spaces and line breaks\n    mark.style.whiteSpace = \"pre\";\n    // do not inherit user-select (it may be `none`)\n    mark.style.webkitUserSelect = \"text\";\n    mark.style.MozUserSelect = \"text\";\n    mark.style.msUserSelect = \"text\";\n    mark.style.userSelect = \"text\";\n    mark.addEventListener(\"copy\", function(e) {\n      e.stopPropagation();\n      if (options.format) {\n        e.preventDefault();\n        if (typeof e.clipboardData === \"undefined\") { // IE 11\n          debug && console.warn(\"unable to use e.clipboardData\");\n          debug && console.warn(\"trying IE specific stuff\");\n          window.clipboardData.clearData();\n          var format = clipboardToIE11Formatting[options.format] || clipboardToIE11Formatting[\"default\"]\n          window.clipboardData.setData(format, text);\n        } else { // all other browsers\n          e.clipboardData.clearData();\n          e.clipboardData.setData(options.format, text);\n        }\n      }\n      if (options.onCopy) {\n        e.preventDefault();\n        options.onCopy(e.clipboardData);\n      }\n    });\n\n    document.body.appendChild(mark);\n\n    range.selectNodeContents(mark);\n    selection.addRange(range);\n\n    var successful = document.execCommand(\"copy\");\n    if (!successful) {\n      throw new Error(\"copy command was unsuccessful\");\n    }\n    success = true;\n  } catch (err) {\n    debug && console.error(\"unable to copy using execCommand: \", err);\n    debug && console.warn(\"trying IE specific stuff\");\n    try {\n      window.clipboardData.setData(options.format || \"text\", text);\n      options.onCopy && options.onCopy(window.clipboardData);\n      success = true;\n    } catch (err) {\n      debug && console.error(\"unable to copy using clipboardData: \", err);\n      debug && console.error(\"falling back to prompt\");\n      message = format(\"message\" in options ? options.message : defaultMessage);\n      window.prompt(message, text);\n    }\n  } finally {\n    if (selection) {\n      if (typeof selection.removeRange == \"function\") {\n        selection.removeRange(range);\n      } else {\n        selection.removeAllRanges();\n      }\n    }\n\n    if (mark) {\n      document.body.removeChild(mark);\n    }\n    reselectPrevious();\n  }\n\n  return success;\n}\n\nmodule.exports = copy;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,eAAe,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAEjD,IAAIC,yBAAyB,GAAG;EAC9B,YAAY,EAAE,MAAM;EACpB,WAAW,EAAE,KAAK;EAClB,SAAS,EAAE;AACb,CAAC;AAED,IAAIC,cAAc,GAAG,kCAAkC;AAEvD,SAASC,MAAMA,CAACC,OAAO,EAAE;EACvB,IAAIC,OAAO,GAAG,CAAC,WAAW,CAACC,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,GAAG,GAAG,GAAG,MAAM,IAAI,IAAI;EAC3E,OAAOJ,OAAO,CAACK,OAAO,CAAC,eAAe,EAAEJ,OAAO,CAAC;AAClD;AAEA,SAASK,IAAIA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC3B,IAAIC,KAAK;IACPT,OAAO;IACPU,gBAAgB;IAChBC,KAAK;IACLC,SAAS;IACTC,IAAI;IACJC,OAAO,GAAG,KAAK;EACjB,IAAI,CAACN,OAAO,EAAE;IACZA,OAAO,GAAG,CAAC,CAAC;EACd;EACAC,KAAK,GAAGD,OAAO,CAACC,KAAK,IAAI,KAAK;EAC9B,IAAI;IACFC,gBAAgB,GAAGf,eAAe,CAAC,CAAC;IAEpCgB,KAAK,GAAGI,QAAQ,CAACC,WAAW,CAAC,CAAC;IAC9BJ,SAAS,GAAGG,QAAQ,CAACE,YAAY,CAAC,CAAC;IAEnCJ,IAAI,GAAGE,QAAQ,CAACG,aAAa,CAAC,MAAM,CAAC;IACrCL,IAAI,CAACM,WAAW,GAAGZ,IAAI;IACvB;IACAM,IAAI,CAACO,UAAU,GAAG,MAAM;IACxB;IACAP,IAAI,CAACQ,KAAK,CAACC,GAAG,GAAG,OAAO;IACxB;IACAT,IAAI,CAACQ,KAAK,CAACE,QAAQ,GAAG,OAAO;IAC7BV,IAAI,CAACQ,KAAK,CAACG,GAAG,GAAG,CAAC;IAClBX,IAAI,CAACQ,KAAK,CAACI,IAAI,GAAG,kBAAkB;IACpC;IACAZ,IAAI,CAACQ,KAAK,CAACK,UAAU,GAAG,KAAK;IAC7B;IACAb,IAAI,CAACQ,KAAK,CAACM,gBAAgB,GAAG,MAAM;IACpCd,IAAI,CAACQ,KAAK,CAACO,aAAa,GAAG,MAAM;IACjCf,IAAI,CAACQ,KAAK,CAACQ,YAAY,GAAG,MAAM;IAChChB,IAAI,CAACQ,KAAK,CAACS,UAAU,GAAG,MAAM;IAC9BjB,IAAI,CAACkB,gBAAgB,CAAC,MAAM,EAAE,UAASC,CAAC,EAAE;MACxCA,CAAC,CAACC,eAAe,CAAC,CAAC;MACnB,IAAIzB,OAAO,CAACT,MAAM,EAAE;QAClBiC,CAAC,CAACE,cAAc,CAAC,CAAC;QAClB,IAAI,OAAOF,CAAC,CAACG,aAAa,KAAK,WAAW,EAAE;UAAE;UAC5C1B,KAAK,IAAI2B,OAAO,CAACC,IAAI,CAAC,+BAA+B,CAAC;UACtD5B,KAAK,IAAI2B,OAAO,CAACC,IAAI,CAAC,0BAA0B,CAAC;UACjDC,MAAM,CAACH,aAAa,CAACI,SAAS,CAAC,CAAC;UAChC,IAAIxC,MAAM,GAAGF,yBAAyB,CAACW,OAAO,CAACT,MAAM,CAAC,IAAIF,yBAAyB,CAAC,SAAS,CAAC;UAC9FyC,MAAM,CAACH,aAAa,CAACK,OAAO,CAACzC,MAAM,EAAEQ,IAAI,CAAC;QAC5C,CAAC,MAAM;UAAE;UACPyB,CAAC,CAACG,aAAa,CAACI,SAAS,CAAC,CAAC;UAC3BP,CAAC,CAACG,aAAa,CAACK,OAAO,CAAChC,OAAO,CAACT,MAAM,EAAEQ,IAAI,CAAC;QAC/C;MACF;MACA,IAAIC,OAAO,CAACiC,MAAM,EAAE;QAClBT,CAAC,CAACE,cAAc,CAAC,CAAC;QAClB1B,OAAO,CAACiC,MAAM,CAACT,CAAC,CAACG,aAAa,CAAC;MACjC;IACF,CAAC,CAAC;IAEFpB,QAAQ,CAAC2B,IAAI,CAACC,WAAW,CAAC9B,IAAI,CAAC;IAE/BF,KAAK,CAACiC,kBAAkB,CAAC/B,IAAI,CAAC;IAC9BD,SAAS,CAACiC,QAAQ,CAAClC,KAAK,CAAC;IAEzB,IAAImC,UAAU,GAAG/B,QAAQ,CAACgC,WAAW,CAAC,MAAM,CAAC;IAC7C,IAAI,CAACD,UAAU,EAAE;MACf,MAAM,IAAIE,KAAK,CAAC,+BAA+B,CAAC;IAClD;IACAlC,OAAO,GAAG,IAAI;EAChB,CAAC,CAAC,OAAOmC,GAAG,EAAE;IACZxC,KAAK,IAAI2B,OAAO,CAACc,KAAK,CAAC,oCAAoC,EAAED,GAAG,CAAC;IACjExC,KAAK,IAAI2B,OAAO,CAACC,IAAI,CAAC,0BAA0B,CAAC;IACjD,IAAI;MACFC,MAAM,CAACH,aAAa,CAACK,OAAO,CAAChC,OAAO,CAACT,MAAM,IAAI,MAAM,EAAEQ,IAAI,CAAC;MAC5DC,OAAO,CAACiC,MAAM,IAAIjC,OAAO,CAACiC,MAAM,CAACH,MAAM,CAACH,aAAa,CAAC;MACtDrB,OAAO,GAAG,IAAI;IAChB,CAAC,CAAC,OAAOmC,GAAG,EAAE;MACZxC,KAAK,IAAI2B,OAAO,CAACc,KAAK,CAAC,sCAAsC,EAAED,GAAG,CAAC;MACnExC,KAAK,IAAI2B,OAAO,CAACc,KAAK,CAAC,wBAAwB,CAAC;MAChDlD,OAAO,GAAGD,MAAM,CAAC,SAAS,IAAIS,OAAO,GAAGA,OAAO,CAACR,OAAO,GAAGF,cAAc,CAAC;MACzEwC,MAAM,CAACa,MAAM,CAACnD,OAAO,EAAEO,IAAI,CAAC;IAC9B;EACF,CAAC,SAAS;IACR,IAAIK,SAAS,EAAE;MACb,IAAI,OAAOA,SAAS,CAACwC,WAAW,IAAI,UAAU,EAAE;QAC9CxC,SAAS,CAACwC,WAAW,CAACzC,KAAK,CAAC;MAC9B,CAAC,MAAM;QACLC,SAAS,CAACyC,eAAe,CAAC,CAAC;MAC7B;IACF;IAEA,IAAIxC,IAAI,EAAE;MACRE,QAAQ,CAAC2B,IAAI,CAACY,WAAW,CAACzC,IAAI,CAAC;IACjC;IACAH,gBAAgB,CAAC,CAAC;EACpB;EAEA,OAAOI,OAAO;AAChB;AAEAyC,MAAM,CAACC,OAAO,GAAGlD,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}