{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport Button from '../button';\nconst PickerButton = props => (/*#__PURE__*/React.createElement(Button, Object.assign({\n  size: \"small\",\n  type: \"primary\"\n}, props)));\nexport default PickerButton;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "createElement", "Object", "assign", "size", "type"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/antd/es/date-picker/PickerButton.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport Button from '../button';\nconst PickerButton = props => (/*#__PURE__*/React.createElement(Button, Object.assign({\n  size: \"small\",\n  type: \"primary\"\n}, props)));\nexport default PickerButton;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,WAAW;AAC9B,MAAMC,YAAY,GAAGC,KAAK,KAAK,aAAaH,KAAK,CAACI,aAAa,CAACH,MAAM,EAAEI,MAAM,CAACC,MAAM,CAAC;EACpFC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE;AACR,CAAC,EAAEL,KAAK,CAAC,CAAC,CAAC;AACX,eAAeD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}