{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport UsbOutlinedSvg from \"@ant-design/icons-svg/es/asn/UsbOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar UsbOutlined = function UsbOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: UsbOutlinedSvg\n  }));\n};\n\n/**![usb](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTc2MCA0MzJWMTQ0YzAtMTcuNy0xNC4zLTMyLTMyLTMySDI5NmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2Mjg4Yy02Ni4yIDAtMTIwIDUyLjEtMTIwIDExNnYzNTZjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOFY1NDhjMC0yNC4zIDIxLjYtNDQgNDguMS00NGg0OTUuOGMyNi41IDAgNDguMSAxOS43IDQ4LjEgNDR2MzU2YzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LThWNTQ4YzAtNjMuOS01My44LTExNi0xMjAtMTE2em0tNDI0IDBWMTg0aDM1MnYyNDhIMzM2em0xMjAtMTg0aC00OGMtNC40IDAtOCAzLjYtOCA4djQ4YzAgNC40IDMuNiA4IDggOGg0OGM0LjQgMCA4LTMuNiA4LTh2LTQ4YzAtNC40LTMuNi04LTgtOHptMTYwIDBoLTQ4Yy00LjQgMC04IDMuNi04IDh2NDhjMCA0LjQgMy42IDggOCA4aDQ4YzQuNCAwIDgtMy42IDgtOHYtNDhjMC00LjQtMy42LTgtOC04eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(UsbOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'UsbOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "UsbOutlinedSvg", "AntdIcon", "UsbOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/augment-projects/quiz/admin_panel/node_modules/antd/node_modules/@ant-design/icons/es/icons/UsbOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport UsbOutlinedSvg from \"@ant-design/icons-svg/es/asn/UsbOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar UsbOutlined = function UsbOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: UsbOutlinedSvg\n  }));\n};\n\n/**![usb](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTc2MCA0MzJWMTQ0YzAtMTcuNy0xNC4zLTMyLTMyLTMySDI5NmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2Mjg4Yy02Ni4yIDAtMTIwIDUyLjEtMTIwIDExNnYzNTZjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOFY1NDhjMC0yNC4zIDIxLjYtNDQgNDguMS00NGg0OTUuOGMyNi41IDAgNDguMSAxOS43IDQ4LjEgNDR2MzU2YzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LThWNTQ4YzAtNjMuOS01My44LTExNi0xMjAtMTE2em0tNDI0IDBWMTg0aDM1MnYyNDhIMzM2em0xMjAtMTg0aC00OGMtNC40IDAtOCAzLjYtOCA4djQ4YzAgNC40IDMuNiA4IDggOGg0OGM0LjQgMCA4LTMuNiA4LTh2LTQ4YzAtNC40LTMuNi04LTgtOHptMTYwIDBoLTQ4Yy00LjQgMC04IDMuNi04IDh2NDhjMCA0LjQgMy42IDggOCA4aDQ4YzQuNCAwIDgtMy42IDgtOHYtNDhjMC00LjQtMy42LTgtOC04eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(UsbOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'UsbOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,WAAW,CAAC;AACxD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,aAAa;AACrC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}