# QUIZ 心理测评系统 - 快速启动指南

## 🚀 5分钟快速体验

### 前置条件
确保您的系统已安装：
- Java 8+ 
- Node.js 16+
- Flutter 3.x
- MySQL 8.0+
- Maven 3.6+

### 第一步：数据库初始化
```bash
# 1. 登录MySQL
mysql -u root -p

# 2. 创建数据库
CREATE DATABASE quiz CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 3. 退出MySQL
exit

# 4. 导入数据表和测试数据
mysql -u root -p quiz < database/init.sql
```

### 第二步：启动后台服务
```bash
# 进入后台服务目录
cd backend_service

# 编译项目
mvn clean compile

# 启动服务 (默认端口8080)
mvn tomcat7:run
```

### 第三步：启动管理后台
```bash
# 新开终端，进入管理后台目录
cd admin_panel

# 安装依赖
npm install

# 启动开发服务器 (默认端口3000)
npm start
```

### 第四步：运行Flutter应用
```bash
# 新开终端，进入Flutter目录
cd flutter_app

# 获取依赖
flutter pub get

# 运行应用 (iOS模拟器或Android设备)
flutter run
```

## 🎯 系统访问地址

- **管理后台**: http://localhost:3000
  - 用户名: `admin`
  - 密码: `admin123`

- **API服务**: http://localhost:8080/quiz-api/api
  - 测评列表: http://localhost:8080/quiz-api/api/quiz/list
  - API文档: 查看各Servlet类的注释

- **Flutter应用**: 通过模拟器或真机运行

## 📱 功能演示流程

### 移动端用户体验
1. **启动应用** → 查看启动页面
2. **用户注册** → 创建新账户
3. **浏览首页** → 查看推荐测评
4. **测评列表** → 选择感兴趣的测评
5. **测评详情** → 了解测评内容
6. **开始答题** → 完成心理测评
7. **查看结果** → 获得分析报告
8. **个人中心** → 管理个人信息

### 管理后台体验
1. **管理员登录** → 进入后台系统
2. **仪表盘** → 查看系统统计
3. **测评管理** → 创建/编辑测评
4. **用户管理** → 查看用户信息
5. **数据分析** → 查看测评数据

## 🧪 系统测试

运行自动化测试脚本：
```bash
# 给脚本执行权限
chmod +x test_system.sh

# 运行系统测试
./test_system.sh
```

测试内容包括：
- ✅ 数据库连接测试
- ✅ API服务测试
- ✅ 用户注册登录测试
- ✅ 测评功能测试
- ✅ 答题流程测试

## 🎨 设计特色

### 柔和粉彩风格
- **薰衣草紫** (#E6D4EF) - 主色调
- **薄荷绿** (#D1E5E9) - 辅助色  
- **浅橙色** (#FFE4CC) - 强调色
- **大圆角** + **柔和阴影** + **卡片布局**

### 用户体验
- 🎯 直观的操作流程
- 🎨 优雅的视觉设计
- 📱 响应式布局适配
- ⚡ 流畅的交互动画

## 📊 项目结构

```
quiz/
├── database/              # 数据库脚本
│   ├── init.sql          # 数据库初始化
│   └── README.md         # 数据库文档
├── backend_service/       # Java后台服务
│   ├── src/main/java/    # Java源码
│   ├── pom.xml           # Maven配置
│   └── README.md         # 后台文档
├── flutter_app/          # Flutter移动应用
│   ├── lib/              # Dart源码
│   ├── pubspec.yaml      # Flutter配置
│   └── README.md         # Flutter文档
├── admin_panel/          # React管理后台
│   ├── src/              # React源码
│   ├── package.json      # NPM配置
│   └── README.md         # React文档
├── DEPLOYMENT_GUIDE.md   # 部署指南
├── test_system.sh        # 系统测试脚本
└── FINAL_PROJECT_REPORT.md # 项目报告
```

## 🔧 常见问题

### Q: 数据库连接失败？
A: 检查MySQL服务状态，确认用户名密码正确

### Q: 后台服务启动失败？
A: 检查8080端口是否被占用，确认Maven配置正确

### Q: Flutter应用无法连接API？
A: 确认API服务正常运行，检查网络权限配置

### Q: 管理后台登录失败？
A: 使用默认账户 admin/admin123，检查认证逻辑

## 📞 技术支持

### 文档资源
- 📖 **数据库设计**: `database/README.md`
- 🔧 **后台API**: `backend_service/README.md`  
- 📱 **Flutter应用**: `flutter_app/README.md`
- 💻 **管理后台**: `admin_panel/README.md`
- 🚀 **部署指南**: `DEPLOYMENT_GUIDE.md`

### 项目特色
- ✨ **现代化技术栈**: Java Servlet + Flutter + React
- 🎨 **优雅UI设计**: 柔和粉彩风格
- 🏗️ **清晰架构**: 分层设计，易于扩展
- 📚 **完善文档**: 详细的开发和部署文档
- 🧪 **自动化测试**: 完整的测试脚本

## 🎉 开始体验

现在您可以开始体验QUIZ心理测评系统了！

1. 按照上述步骤启动各个服务
2. 访问管理后台创建测评内容
3. 使用Flutter应用体验完整流程
4. 运行测试脚本验证系统功能

享受您的心理测评之旅！ 🌟
