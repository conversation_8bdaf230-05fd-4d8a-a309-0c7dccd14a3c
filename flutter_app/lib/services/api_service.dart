import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ApiService {
  static const String baseUrl = 'http://localhost:8080/quiz-api/api';
  
  late final Dio _dio;
  static ApiService? _instance;
  
  ApiService._internal() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 10),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));
    
    _setupInterceptors();
  }
  
  static ApiService get instance {
    _instance ??= ApiService._internal();
    return _instance!;
  }
  
  void _setupInterceptors() {
    // 请求拦截器 - 添加认证token
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        final prefs = await SharedPreferences.getInstance();
        final token = prefs.getString('auth_token');
        
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        
        handler.next(options);
      },
      onResponse: (response, handler) {
        // 统一处理响应格式
        if (response.data is Map<String, dynamic>) {
          final data = response.data as Map<String, dynamic>;
          if (data['code'] == 200) {
            response.data = {
              'success': true,
              'data': data['data'],
              'message': data['message'],
            };
          } else {
            response.data = {
              'success': false,
              'data': null,
              'message': data['message'] ?? '请求失败',
            };
          }
        }
        
        handler.next(response);
      },
      onError: (error, handler) async {
        // 处理401错误 - token过期
        if (error.response?.statusCode == 401) {
          await _handleTokenExpired();
        }
        
        // 统一错误格式
        final errorData = {
          'success': false,
          'data': null,
          'message': _getErrorMessage(error),
        };
        
        error.response?.data = errorData;
        handler.next(error);
      },
    ));
  }
  
  /// 处理token过期
  Future<void> _handleTokenExpired() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
    await prefs.remove('user_data');
    // 这里可以发送通知让应用跳转到登录页
  }
  
  /// 获取错误信息
  String _getErrorMessage(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return '连接超时，请检查网络';
      case DioExceptionType.sendTimeout:
        return '发送超时，请检查网络';
      case DioExceptionType.receiveTimeout:
        return '接收超时，请检查网络';
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        switch (statusCode) {
          case 400:
            return '请求参数错误';
          case 401:
            return '未授权，请重新登录';
          case 403:
            return '禁止访问';
          case 404:
            return '请求的资源不存在';
          case 500:
            return '服务器内部错误';
          default:
            return '请求失败 ($statusCode)';
        }
      case DioExceptionType.cancel:
        return '请求已取消';
      case DioExceptionType.connectionError:
        return '网络连接错误';
      case DioExceptionType.unknown:
        return '未知错误';
      default:
        return '网络错误';
    }
  }
  
  /// GET请求
  Future<Map<String, dynamic>> get(
    String path, {
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      return response.data as Map<String, dynamic>;
    } on DioException catch (e) {
      return e.response?.data as Map<String, dynamic>? ?? {
        'success': false,
        'data': null,
        'message': _getErrorMessage(e),
      };
    }
  }
  
  /// POST请求
  Future<Map<String, dynamic>> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
      );
      return response.data as Map<String, dynamic>;
    } on DioException catch (e) {
      return e.response?.data as Map<String, dynamic>? ?? {
        'success': false,
        'data': null,
        'message': _getErrorMessage(e),
      };
    }
  }
  
  /// PUT请求
  Future<Map<String, dynamic>> put(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
      );
      return response.data as Map<String, dynamic>;
    } on DioException catch (e) {
      return e.response?.data as Map<String, dynamic>? ?? {
        'success': false,
        'data': null,
        'message': _getErrorMessage(e),
      };
    }
  }
  
  /// DELETE请求
  Future<Map<String, dynamic>> delete(
    String path, {
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _dio.delete(path, queryParameters: queryParameters);
      return response.data as Map<String, dynamic>;
    } on DioException catch (e) {
      return e.response?.data as Map<String, dynamic>? ?? {
        'success': false,
        'data': null,
        'message': _getErrorMessage(e),
      };
    }
  }
}
