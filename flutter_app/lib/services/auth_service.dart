import 'api_service.dart';

class AuthService {
  final ApiService _apiService = ApiService.instance;

  /// 用户登录
  Future<Map<String, dynamic>> login(String username, String password) async {
    return await _apiService.post('/auth/login', data: {
      'username': username,
      'password': password,
    });
  }

  /// 用户注册
  Future<Map<String, dynamic>> register(
    String username,
    String email,
    String password, {
    String? nickname,
  }) async {
    return await _apiService.post('/auth/register', data: {
      'username': username,
      'email': email,
      'password': password,
      'nickname': nickname,
    });
  }

  /// 刷新Token
  Future<Map<String, dynamic>> refreshToken(String token) async {
    return await _apiService.post('/auth/refresh');
  }

  /// 获取用户信息
  Future<Map<String, dynamic>> getUserProfile() async {
    return await _apiService.get('/user/profile');
  }

  /// 更新用户信息
  Future<Map<String, dynamic>> updateProfile(Map<String, dynamic> userData) async {
    return await _apiService.put('/user/profile', data: userData);
  }

  /// 修改密码
  Future<Map<String, dynamic>> changePassword(
    String oldPassword,
    String newPassword,
  ) async {
    return await _apiService.put('/user/password', data: {
      'oldPassword': oldPassword,
      'newPassword': newPassword,
    });
  }

  /// 上传头像
  Future<Map<String, dynamic>> uploadAvatar(String imagePath) async {
    // TODO: 实现文件上传
    return {
      'success': false,
      'message': '文件上传功能待实现',
    };
  }
}
