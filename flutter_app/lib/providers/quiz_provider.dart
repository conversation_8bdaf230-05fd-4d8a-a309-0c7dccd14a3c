import 'package:flutter/foundation.dart';
import '../models/quiz.dart';
import '../models/question.dart';
import '../models/quiz_result.dart';
import '../services/quiz_service.dart';

class QuizProvider with ChangeNotifier {
  final QuizService _quizService = QuizService();
  
  // 测评列表相关
  List<Quiz> _quizzes = [];
  List<Quiz> _featuredQuizzes = [];
  bool _isLoadingQuizzes = false;
  
  // 当前测评相关
  Quiz? _currentQuiz;
  List<Question> _questions = [];
  Map<int, dynamic> _answers = {};
  int _currentQuestionIndex = 0;
  bool _isLoadingQuiz = false;
  
  // 测评结果相关
  QuizResult? _currentResult;
  List<QuizResult> _userResults = [];
  bool _isLoadingResult = false;
  
  String? _error;

  // Getters
  List<Quiz> get quizzes => _quizzes;
  List<Quiz> get featuredQuizzes => _featuredQuizzes;
  bool get isLoadingQuizzes => _isLoadingQuizzes;
  
  Quiz? get currentQuiz => _currentQuiz;
  List<Question> get questions => _questions;
  Map<int, dynamic> get answers => _answers;
  int get currentQuestionIndex => _currentQuestionIndex;
  bool get isLoadingQuiz => _isLoadingQuiz;
  
  QuizResult? get currentResult => _currentResult;
  List<QuizResult> get userResults => _userResults;
  bool get isLoadingResult => _isLoadingResult;
  
  String? get error => _error;
  
  // 计算属性
  bool get hasNextQuestion => _currentQuestionIndex < _questions.length - 1;
  bool get hasPreviousQuestion => _currentQuestionIndex > 0;
  double get progress => _questions.isEmpty ? 0.0 : (_currentQuestionIndex + 1) / _questions.length;
  Question? get currentQuestion => _questions.isEmpty ? null : _questions[_currentQuestionIndex];

  /// 获取测评列表
  Future<void> fetchQuizzes({String? category}) async {
    _isLoadingQuizzes = true;
    _clearError();
    notifyListeners();

    try {
      final result = await _quizService.getQuizzes(category: category);
      
      if (result['success'] == true) {
        _quizzes = (result['data'] as List)
            .map((json) => Quiz.fromMap(json))
            .toList();
      } else {
        _setError(result['message'] ?? '获取测评列表失败');
      }
    } catch (e) {
      _setError('获取测评列表失败: $e');
    } finally {
      _isLoadingQuizzes = false;
      notifyListeners();
    }
  }

  /// 获取推荐测评
  Future<void> fetchFeaturedQuizzes() async {
    try {
      final result = await _quizService.getFeaturedQuizzes();
      
      if (result['success'] == true) {
        _featuredQuizzes = (result['data'] as List)
            .map((json) => Quiz.fromMap(json))
            .toList();
        notifyListeners();
      }
    } catch (e) {
      debugPrint('获取推荐测评失败: $e');
    }
  }

  /// 获取测评详情
  Future<bool> fetchQuizDetail(String quizId) async {
    _isLoadingQuiz = true;
    _clearError();
    notifyListeners();

    try {
      final result = await _quizService.getQuizDetail(quizId);
      
      if (result['success'] == true) {
        _currentQuiz = Quiz.fromMap(result['data']);
        _isLoadingQuiz = false;
        notifyListeners();
        return true;
      } else {
        _setError(result['message'] ?? '获取测评详情失败');
        _isLoadingQuiz = false;
        return false;
      }
    } catch (e) {
      _setError('获取测评详情失败: $e');
      _isLoadingQuiz = false;
      return false;
    }
  }

  /// 开始测评，获取题目
  Future<bool> startQuiz(String quizId) async {
    _isLoadingQuiz = true;
    _clearError();
    _resetQuizState();
    notifyListeners();

    try {
      final result = await _quizService.getQuizQuestions(quizId);
      
      if (result['success'] == true) {
        _questions = (result['data'] as List)
            .map((json) => Question.fromMap(json))
            .toList();
        _currentQuestionIndex = 0;
        _isLoadingQuiz = false;
        notifyListeners();
        return true;
      } else {
        _setError(result['message'] ?? '获取题目失败');
        _isLoadingQuiz = false;
        return false;
      }
    } catch (e) {
      _setError('获取题目失败: $e');
      _isLoadingQuiz = false;
      return false;
    }
  }

  /// 回答问题
  void answerQuestion(int questionId, dynamic answer) {
    _answers[questionId] = answer;
    notifyListeners();
  }

  /// 下一题
  void nextQuestion() {
    if (hasNextQuestion) {
      _currentQuestionIndex++;
      notifyListeners();
    }
  }

  /// 上一题
  void previousQuestion() {
    if (hasPreviousQuestion) {
      _currentQuestionIndex--;
      notifyListeners();
    }
  }

  /// 跳转到指定题目
  void goToQuestion(int index) {
    if (index >= 0 && index < _questions.length) {
      _currentQuestionIndex = index;
      notifyListeners();
    }
  }

  /// 提交测评
  Future<bool> submitQuiz(String quizId) async {
    _isLoadingResult = true;
    _clearError();
    notifyListeners();

    try {
      final result = await _quizService.submitQuiz(quizId, _answers);
      
      if (result['success'] == true) {
        _currentResult = QuizResult.fromMap(result['data']);
        _isLoadingResult = false;
        notifyListeners();
        return true;
      } else {
        _setError(result['message'] ?? '提交测评失败');
        _isLoadingResult = false;
        return false;
      }
    } catch (e) {
      _setError('提交测评失败: $e');
      _isLoadingResult = false;
      return false;
    }
  }

  /// 获取测评结果
  Future<bool> fetchQuizResult(String recordId) async {
    _isLoadingResult = true;
    _clearError();
    notifyListeners();

    try {
      final result = await _quizService.getQuizResult(recordId);
      
      if (result['success'] == true) {
        _currentResult = QuizResult.fromMap(result['data']);
        _isLoadingResult = false;
        notifyListeners();
        return true;
      } else {
        _setError(result['message'] ?? '获取结果失败');
        _isLoadingResult = false;
        return false;
      }
    } catch (e) {
      _setError('获取结果失败: $e');
      _isLoadingResult = false;
      return false;
    }
  }

  /// 获取用户历史结果
  Future<void> fetchUserResults() async {
    try {
      final result = await _quizService.getUserResults();
      
      if (result['success'] == true) {
        _userResults = (result['data'] as List)
            .map((json) => QuizResult.fromMap(json))
            .toList();
        notifyListeners();
      }
    } catch (e) {
      debugPrint('获取历史结果失败: $e');
    }
  }

  /// 重置测评状态
  void _resetQuizState() {
    _questions.clear();
    _answers.clear();
    _currentQuestionIndex = 0;
    _currentResult = null;
  }

  /// 设置错误信息
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// 清除错误信息
  void _clearError() {
    _error = null;
  }

  /// 清除当前测评
  void clearCurrentQuiz() {
    _currentQuiz = null;
    _resetQuizState();
    notifyListeners();
  }
}
