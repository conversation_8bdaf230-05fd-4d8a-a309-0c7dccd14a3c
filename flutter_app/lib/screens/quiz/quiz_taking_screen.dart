import 'package:flutter/material.dart';
import '../../utils/app_theme.dart';

class QuizTakingScreen extends StatelessWidget {
  final String quizId;
  
  const QuizTakingScreen({super.key, required this.quizId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('答题中'),
        backgroundColor: Colors.white,
        foregroundColor: AppTheme.textPrimary,
        elevation: 0,
      ),
      body: Center(
        child: Text(
          '答题页面\n测评ID: $quizId\n（待开发）',
          textAlign: TextAlign.center,
          style: const TextStyle(
            fontSize: 18,
            color: AppTheme.textSecondary,
          ),
        ),
      ),
    );
  }
}
