import 'package:flutter/material.dart';
import '../../utils/app_theme.dart';

class QuizResultScreen extends StatelessWidget {
  final String recordId;
  
  const QuizResultScreen({super.key, required this.recordId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('测评结果'),
        backgroundColor: Colors.white,
        foregroundColor: AppTheme.textPrimary,
        elevation: 0,
      ),
      body: Center(
        child: Text(
          '测评结果页面\n记录ID: $recordId\n（待开发）',
          textAlign: TextAlign.center,
          style: const TextStyle(
            fontSize: 18,
            color: AppTheme.textSecondary,
          ),
        ),
      ),
    );
  }
}
