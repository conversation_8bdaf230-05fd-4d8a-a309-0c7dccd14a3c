import 'dart:convert';

class Quiz {
  final int id;
  final String title;
  final String? subtitle;
  final String description;
  final int? categoryId;
  final String? categoryName;
  final String? coverImage;
  final int questionCount;
  final int estimatedTime; // 预估时间（分钟）
  final int difficultyLevel; // 1-简单, 2-中等, 3-困难
  final bool isFree;
  final double price;
  final bool isFeatured;
  final int viewCount;
  final int takeCount;
  final int status; // 0-下架, 1-上架
  final DateTime createdAt;
  final DateTime? updatedAt;

  Quiz({
    required this.id,
    required this.title,
    this.subtitle,
    required this.description,
    this.categoryId,
    this.categoryName,
    this.coverImage,
    required this.questionCount,
    required this.estimatedTime,
    required this.difficultyLevel,
    required this.isFree,
    required this.price,
    required this.isFeatured,
    required this.viewCount,
    required this.takeCount,
    required this.status,
    required this.createdAt,
    this.updatedAt,
  });

  /// 从Map创建Quiz对象
  factory Quiz.fromMap(Map<String, dynamic> map) {
    return Quiz(
      id: map['id']?.toInt() ?? 0,
      title: map['title'] ?? '',
      subtitle: map['subtitle'],
      description: map['description'] ?? '',
      categoryId: map['categoryId']?.toInt(),
      categoryName: map['categoryName'],
      coverImage: map['coverImage'],
      questionCount: map['questionCount']?.toInt() ?? 0,
      estimatedTime: map['estimatedTime']?.toInt() ?? 0,
      difficultyLevel: map['difficultyLevel']?.toInt() ?? 1,
      isFree: map['isFree'] ?? true,
      price: (map['price'] ?? 0.0).toDouble(),
      isFeatured: map['isFeatured'] ?? false,
      viewCount: map['viewCount']?.toInt() ?? 0,
      takeCount: map['takeCount']?.toInt() ?? 0,
      status: map['status']?.toInt() ?? 1,
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: map['updatedAt'] != null ? DateTime.parse(map['updatedAt']) : null,
    );
  }

  /// 从JSON字符串创建Quiz对象
  factory Quiz.fromJson(String source) => Quiz.fromMap(json.decode(source));

  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'subtitle': subtitle,
      'description': description,
      'categoryId': categoryId,
      'categoryName': categoryName,
      'coverImage': coverImage,
      'questionCount': questionCount,
      'estimatedTime': estimatedTime,
      'difficultyLevel': difficultyLevel,
      'isFree': isFree,
      'price': price,
      'isFeatured': isFeatured,
      'viewCount': viewCount,
      'takeCount': takeCount,
      'status': status,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// 转换为JSON字符串
  String toJson() => json.encode(toMap());

  /// 复制并修改部分属性
  Quiz copyWith({
    int? id,
    String? title,
    String? subtitle,
    String? description,
    int? categoryId,
    String? categoryName,
    String? coverImage,
    int? questionCount,
    int? estimatedTime,
    int? difficultyLevel,
    bool? isFree,
    double? price,
    bool? isFeatured,
    int? viewCount,
    int? takeCount,
    int? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Quiz(
      id: id ?? this.id,
      title: title ?? this.title,
      subtitle: subtitle ?? this.subtitle,
      description: description ?? this.description,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      coverImage: coverImage ?? this.coverImage,
      questionCount: questionCount ?? this.questionCount,
      estimatedTime: estimatedTime ?? this.estimatedTime,
      difficultyLevel: difficultyLevel ?? this.difficultyLevel,
      isFree: isFree ?? this.isFree,
      price: price ?? this.price,
      isFeatured: isFeatured ?? this.isFeatured,
      viewCount: viewCount ?? this.viewCount,
      takeCount: takeCount ?? this.takeCount,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// 获取难度文本
  String get difficultyText {
    switch (difficultyLevel) {
      case 1:
        return '简单';
      case 2:
        return '中等';
      case 3:
        return '困难';
      default:
        return '未知';
    }
  }

  /// 获取预估时间文本
  String get estimatedTimeText {
    if (estimatedTime <= 0) return '未知';
    if (estimatedTime < 60) return '${estimatedTime}分钟';
    final hours = estimatedTime ~/ 60;
    final minutes = estimatedTime % 60;
    if (minutes == 0) return '${hours}小时';
    return '${hours}小时${minutes}分钟';
  }

  /// 获取价格文本
  String get priceText {
    if (isFree) return '免费';
    return '¥${price.toStringAsFixed(2)}';
  }

  /// 是否可以参与（上架且有效）
  bool get isAvailable => status == 1;

  @override
  String toString() {
    return 'Quiz(id: $id, title: $title, questionCount: $questionCount, isFree: $isFree)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Quiz && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}
