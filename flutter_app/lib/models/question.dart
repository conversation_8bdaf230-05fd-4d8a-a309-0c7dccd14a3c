import 'dart:convert';

class Question {
  final int id;
  final int quizId;
  final String questionText;
  final int questionType; // 1-单选, 2-多选, 3-滑动打分
  final String? imageUrl;
  final int sortOrder;
  final bool isRequired;
  final List<QuestionOption> options;

  Question({
    required this.id,
    required this.quizId,
    required this.questionText,
    required this.questionType,
    this.imageUrl,
    required this.sortOrder,
    required this.isRequired,
    required this.options,
  });

  /// 从Map创建Question对象
  factory Question.fromMap(Map<String, dynamic> map) {
    return Question(
      id: map['id']?.toInt() ?? 0,
      quizId: map['quizId']?.toInt() ?? 0,
      questionText: map['questionText'] ?? '',
      questionType: map['questionType']?.toInt() ?? 1,
      imageUrl: map['imageUrl'],
      sortOrder: map['sortOrder']?.toInt() ?? 0,
      isRequired: map['isRequired'] ?? true,
      options: List<QuestionOption>.from(
        (map['options'] ?? []).map((x) => QuestionOption.fromMap(x)),
      ),
    );
  }

  /// 从JSON字符串创建Question对象
  factory Question.fromJson(String source) => Question.fromMap(json.decode(source));

  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'quizId': quizId,
      'questionText': questionText,
      'questionType': questionType,
      'imageUrl': imageUrl,
      'sortOrder': sortOrder,
      'isRequired': isRequired,
      'options': options.map((x) => x.toMap()).toList(),
    };
  }

  /// 转换为JSON字符串
  String toJson() => json.encode(toMap());

  /// 获取问题类型文本
  String get questionTypeText {
    switch (questionType) {
      case 1:
        return '单选题';
      case 2:
        return '多选题';
      case 3:
        return '滑动打分';
      default:
        return '未知类型';
    }
  }

  /// 是否为单选题
  bool get isSingleChoice => questionType == 1;

  /// 是否为多选题
  bool get isMultipleChoice => questionType == 2;

  /// 是否为滑动打分题
  bool get isSliderQuestion => questionType == 3;

  @override
  String toString() {
    return 'Question(id: $id, questionText: $questionText, questionType: $questionType)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Question && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}

class QuestionOption {
  final int id;
  final int questionId;
  final String optionText;
  final String? optionValue;
  final int score;
  final int sortOrder;

  QuestionOption({
    required this.id,
    required this.questionId,
    required this.optionText,
    this.optionValue,
    required this.score,
    required this.sortOrder,
  });

  /// 从Map创建QuestionOption对象
  factory QuestionOption.fromMap(Map<String, dynamic> map) {
    return QuestionOption(
      id: map['id']?.toInt() ?? 0,
      questionId: map['questionId']?.toInt() ?? 0,
      optionText: map['optionText'] ?? '',
      optionValue: map['optionValue'],
      score: map['score']?.toInt() ?? 0,
      sortOrder: map['sortOrder']?.toInt() ?? 0,
    );
  }

  /// 从JSON字符串创建QuestionOption对象
  factory QuestionOption.fromJson(String source) => QuestionOption.fromMap(json.decode(source));

  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'questionId': questionId,
      'optionText': optionText,
      'optionValue': optionValue,
      'score': score,
      'sortOrder': sortOrder,
    };
  }

  /// 转换为JSON字符串
  String toJson() => json.encode(toMap());

  @override
  String toString() {
    return 'QuestionOption(id: $id, optionText: $optionText, score: $score)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is QuestionOption && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}
