import 'dart:convert';

class QuizResult {
  final int id;
  final int userId;
  final int quizId;
  final String quizTitle;
  final DateTime startTime;
  final DateTime? endTime;
  final int totalScore;
  final String? resultType;
  final String? resultTitle;
  final String? resultDescription;
  final String? detailedAnalysis;
  final String? suggestions;
  final String? resultImage;
  final int status; // 0-进行中, 1-已完成, 2-已放弃
  final List<DimensionScore>? dimensionScores;

  QuizResult({
    required this.id,
    required this.userId,
    required this.quizId,
    required this.quizTitle,
    required this.startTime,
    this.endTime,
    required this.totalScore,
    this.resultType,
    this.resultTitle,
    this.resultDescription,
    this.detailedAnalysis,
    this.suggestions,
    this.resultImage,
    required this.status,
    this.dimensionScores,
  });

  /// 从Map创建QuizResult对象
  factory QuizResult.fromMap(Map<String, dynamic> map) {
    return QuizResult(
      id: map['id']?.toInt() ?? 0,
      userId: map['userId']?.toInt() ?? 0,
      quizId: map['quizId']?.toInt() ?? 0,
      quizTitle: map['quizTitle'] ?? '',
      startTime: DateTime.parse(map['startTime']),
      endTime: map['endTime'] != null ? DateTime.parse(map['endTime']) : null,
      totalScore: map['totalScore']?.toInt() ?? 0,
      resultType: map['resultType'],
      resultTitle: map['resultTitle'],
      resultDescription: map['resultDescription'],
      detailedAnalysis: map['detailedAnalysis'],
      suggestions: map['suggestions'],
      resultImage: map['resultImage'],
      status: map['status']?.toInt() ?? 0,
      dimensionScores: map['dimensionScores'] != null
          ? List<DimensionScore>.from(
              (map['dimensionScores'] as List).map((x) => DimensionScore.fromMap(x)),
            )
          : null,
    );
  }

  /// 从JSON字符串创建QuizResult对象
  factory QuizResult.fromJson(String source) => QuizResult.fromMap(json.decode(source));

  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'quizId': quizId,
      'quizTitle': quizTitle,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'totalScore': totalScore,
      'resultType': resultType,
      'resultTitle': resultTitle,
      'resultDescription': resultDescription,
      'detailedAnalysis': detailedAnalysis,
      'suggestions': suggestions,
      'resultImage': resultImage,
      'status': status,
      'dimensionScores': dimensionScores?.map((x) => x.toMap()).toList(),
    };
  }

  /// 转换为JSON字符串
  String toJson() => json.encode(toMap());

  /// 获取状态文本
  String get statusText {
    switch (status) {
      case 0:
        return '进行中';
      case 1:
        return '已完成';
      case 2:
        return '已放弃';
      default:
        return '未知';
    }
  }

  /// 是否已完成
  bool get isCompleted => status == 1;

  /// 是否进行中
  bool get isInProgress => status == 0;

  /// 获取完成时长
  Duration? get completionDuration {
    if (endTime == null) return null;
    return endTime!.difference(startTime);
  }

  /// 获取完成时长文本
  String get completionDurationText {
    final duration = completionDuration;
    if (duration == null) return '未完成';
    
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    
    if (minutes > 0) {
      return '${minutes}分${seconds}秒';
    } else {
      return '${seconds}秒';
    }
  }

  @override
  String toString() {
    return 'QuizResult(id: $id, quizTitle: $quizTitle, totalScore: $totalScore, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is QuizResult && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}

class DimensionScore {
  final String dimension;
  final String dimensionName;
  final int score;
  final int maxScore;
  final String? description;

  DimensionScore({
    required this.dimension,
    required this.dimensionName,
    required this.score,
    required this.maxScore,
    this.description,
  });

  /// 从Map创建DimensionScore对象
  factory DimensionScore.fromMap(Map<String, dynamic> map) {
    return DimensionScore(
      dimension: map['dimension'] ?? '',
      dimensionName: map['dimensionName'] ?? '',
      score: map['score']?.toInt() ?? 0,
      maxScore: map['maxScore']?.toInt() ?? 100,
      description: map['description'],
    );
  }

  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'dimension': dimension,
      'dimensionName': dimensionName,
      'score': score,
      'maxScore': maxScore,
      'description': description,
    };
  }

  /// 获取得分百分比
  double get percentage => maxScore > 0 ? (score / maxScore) : 0.0;

  /// 获取得分等级
  String get level {
    final percent = percentage;
    if (percent >= 0.8) return '优秀';
    if (percent >= 0.6) return '良好';
    if (percent >= 0.4) return '一般';
    if (percent >= 0.2) return '较低';
    return '很低';
  }

  @override
  String toString() {
    return 'DimensionScore(dimension: $dimension, score: $score, maxScore: $maxScore)';
  }
}
