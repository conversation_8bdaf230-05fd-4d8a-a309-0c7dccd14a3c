import 'dart:convert';

class User {
  final int id;
  final String username;
  final String email;
  final String? phone;
  final String nickname;
  final String? avatarUrl;
  final int? gender; // 0-未知, 1-男, 2-女
  final DateTime? birthDate;
  final bool isVip;
  final DateTime? vipExpireTime;
  final int status; // 0-禁用, 1-正常
  final DateTime createdAt;
  final DateTime? updatedAt;

  User({
    required this.id,
    required this.username,
    required this.email,
    this.phone,
    required this.nickname,
    this.avatarUrl,
    this.gender,
    this.birthDate,
    required this.isVip,
    this.vipExpireTime,
    required this.status,
    required this.createdAt,
    this.updatedAt,
  });

  /// 从Map创建User对象
  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id']?.toInt() ?? 0,
      username: map['username'] ?? '',
      email: map['email'] ?? '',
      phone: map['phone'],
      nickname: map['nickname'] ?? '',
      avatarUrl: map['avatarUrl'],
      gender: map['gender']?.toInt(),
      birthDate: map['birthDate'] != null ? DateTime.parse(map['birthDate']) : null,
      isVip: map['isVip'] ?? false,
      vipExpireTime: map['vipExpireTime'] != null ? DateTime.parse(map['vipExpireTime']) : null,
      status: map['status']?.toInt() ?? 1,
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: map['updatedAt'] != null ? DateTime.parse(map['updatedAt']) : null,
    );
  }

  /// 从JSON字符串创建User对象
  factory User.fromJson(String source) => User.fromMap(json.decode(source));

  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'phone': phone,
      'nickname': nickname,
      'avatarUrl': avatarUrl,
      'gender': gender,
      'birthDate': birthDate?.toIso8601String(),
      'isVip': isVip,
      'vipExpireTime': vipExpireTime?.toIso8601String(),
      'status': status,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// 转换为JSON字符串
  String toJson() => json.encode(toMap());

  /// 复制并修改部分属性
  User copyWith({
    int? id,
    String? username,
    String? email,
    String? phone,
    String? nickname,
    String? avatarUrl,
    int? gender,
    DateTime? birthDate,
    bool? isVip,
    DateTime? vipExpireTime,
    int? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      nickname: nickname ?? this.nickname,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      gender: gender ?? this.gender,
      birthDate: birthDate ?? this.birthDate,
      isVip: isVip ?? this.isVip,
      vipExpireTime: vipExpireTime ?? this.vipExpireTime,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// 获取性别文本
  String get genderText {
    switch (gender) {
      case 1:
        return '男';
      case 2:
        return '女';
      default:
        return '未知';
    }
  }

  /// 获取年龄
  int? get age {
    if (birthDate == null) return null;
    final now = DateTime.now();
    int age = now.year - birthDate!.year;
    if (now.month < birthDate!.month || 
        (now.month == birthDate!.month && now.day < birthDate!.day)) {
      age--;
    }
    return age;
  }

  /// 是否VIP有效
  bool get isVipValid {
    if (!isVip) return false;
    if (vipExpireTime == null) return true;
    return DateTime.now().isBefore(vipExpireTime!);
  }

  @override
  String toString() {
    return 'User(id: $id, username: $username, email: $email, nickname: $nickname, isVip: $isVip)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}
