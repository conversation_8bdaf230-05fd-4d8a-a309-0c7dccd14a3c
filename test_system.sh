#!/bin/bash

# QUIZ 心理测评系统 - 系统测试脚本
# 用于验证整个系统的功能是否正常

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
API_BASE_URL="http://localhost:8080/quiz-api/api"
ADMIN_URL="http://localhost:3000"
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="quiz"
DB_USER="root"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务状态
check_service() {
    local service_name=$1
    local url=$2
    local expected_status=${3:-200}
    
    log_info "检查 $service_name 服务状态..."
    
    if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "$expected_status"; then
        log_success "$service_name 服务正常运行"
        return 0
    else
        log_error "$service_name 服务无法访问"
        return 1
    fi
}

# 检查数据库连接
check_database() {
    log_info "检查数据库连接..."
    
    if command -v mysql >/dev/null 2>&1; then
        if mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME; SELECT 1;" >/dev/null 2>&1; then
            log_success "数据库连接正常"
            return 0
        else
            log_error "数据库连接失败"
            return 1
        fi
    else
        log_warning "MySQL客户端未安装，跳过数据库检查"
        return 0
    fi
}

# 测试用户注册
test_user_registration() {
    log_info "测试用户注册功能..."
    
    local test_user="testuser_$(date +%s)"
    local test_email="$<EMAIL>"
    local test_password="123456"
    
    local response=$(curl -s -X POST "$API_BASE_URL/auth/register" \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"$test_user\",\"email\":\"$test_email\",\"password\":\"$test_password\"}")
    
    if echo "$response" | grep -q "success.*true\|token"; then
        log_success "用户注册功能正常"
        echo "$test_user:$test_password" > /tmp/quiz_test_user.txt
        return 0
    else
        log_error "用户注册功能异常: $response"
        return 1
    fi
}

# 测试用户登录
test_user_login() {
    log_info "测试用户登录功能..."
    
    if [ ! -f /tmp/quiz_test_user.txt ]; then
        log_warning "未找到测试用户，跳过登录测试"
        return 0
    fi
    
    local user_info=$(cat /tmp/quiz_test_user.txt)
    local username=$(echo "$user_info" | cut -d: -f1)
    local password=$(echo "$user_info" | cut -d: -f2)
    
    local response=$(curl -s -X POST "$API_BASE_URL/auth/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"$username\",\"password\":\"$password\"}")
    
    if echo "$response" | grep -q "success.*true\|token"; then
        log_success "用户登录功能正常"
        # 提取token用于后续测试
        local token=$(echo "$response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
        echo "$token" > /tmp/quiz_test_token.txt
        return 0
    else
        log_error "用户登录功能异常: $response"
        return 1
    fi
}

# 测试获取测评列表
test_quiz_list() {
    log_info "测试获取测评列表功能..."
    
    local response=$(curl -s "$API_BASE_URL/quiz/list")
    
    if echo "$response" | grep -q "success.*true\|list\|quizzes"; then
        log_success "获取测评列表功能正常"
        return 0
    else
        log_error "获取测评列表功能异常: $response"
        return 1
    fi
}

# 测试获取测评详情
test_quiz_detail() {
    log_info "测试获取测评详情功能..."
    
    local response=$(curl -s "$API_BASE_URL/quiz/1")
    
    if echo "$response" | grep -q "success.*true\|title\|description"; then
        log_success "获取测评详情功能正常"
        return 0
    else
        log_error "获取测评详情功能异常: $response"
        return 1
    fi
}

# 测试获取测评题目
test_quiz_questions() {
    log_info "测试获取测评题目功能..."
    
    local response=$(curl -s "$API_BASE_URL/quiz/1/questions")
    
    if echo "$response" | grep -q "success.*true\|questions\|questionText"; then
        log_success "获取测评题目功能正常"
        return 0
    else
        log_error "获取测评题目功能异常: $response"
        return 1
    fi
}

# 测试答题功能
test_quiz_submission() {
    log_info "测试答题提交功能..."
    
    if [ ! -f /tmp/quiz_test_token.txt ]; then
        log_warning "未找到认证token，跳过答题测试"
        return 0
    fi
    
    local token=$(cat /tmp/quiz_test_token.txt)
    
    # 先开始测评
    local start_response=$(curl -s -X POST "$API_BASE_URL/answer/start" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $token" \
        -d '{"quizId":1}')
    
    if echo "$start_response" | grep -q "success.*true\|recordId"; then
        log_success "开始测评功能正常"
        
        # 提取recordId
        local record_id=$(echo "$start_response" | grep -o '"recordId":[0-9]*' | cut -d: -f2)
        
        # 提交答案
        local submit_response=$(curl -s -X POST "$API_BASE_URL/answer/submit" \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $token" \
            -d "{\"recordId\":$record_id,\"answers\":{\"1\":1,\"2\":2}}")
        
        if echo "$submit_response" | grep -q "success.*true\|totalScore"; then
            log_success "答题提交功能正常"
            return 0
        else
            log_error "答题提交功能异常: $submit_response"
            return 1
        fi
    else
        log_error "开始测评功能异常: $start_response"
        return 1
    fi
}

# 检查项目文件结构
check_project_structure() {
    log_info "检查项目文件结构..."
    
    local required_files=(
        "database/init.sql"
        "backend_service/pom.xml"
        "flutter_app/pubspec.yaml"
        "admin_panel/package.json"
    )
    
    local missing_files=()
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -eq 0 ]; then
        log_success "项目文件结构完整"
        return 0
    else
        log_error "缺少以下文件: ${missing_files[*]}"
        return 1
    fi
}

# 生成测试报告
generate_report() {
    local total_tests=$1
    local passed_tests=$2
    local failed_tests=$((total_tests - passed_tests))
    
    echo ""
    echo "=================================="
    echo "         测试报告"
    echo "=================================="
    echo "总测试数: $total_tests"
    echo "通过测试: $passed_tests"
    echo "失败测试: $failed_tests"
    echo "成功率: $(( passed_tests * 100 / total_tests ))%"
    echo "=================================="
    
    if [ $failed_tests -eq 0 ]; then
        log_success "所有测试通过！系统运行正常。"
        return 0
    else
        log_error "有 $failed_tests 个测试失败，请检查系统配置。"
        return 1
    fi
}

# 主测试流程
main() {
    echo "开始QUIZ心理测评系统测试..."
    echo ""
    
    local total_tests=0
    local passed_tests=0
    
    # 检查项目结构
    ((total_tests++))
    if check_project_structure; then
        ((passed_tests++))
    fi
    
    # 检查数据库
    ((total_tests++))
    if check_database; then
        ((passed_tests++))
    fi
    
    # 检查后台服务
    ((total_tests++))
    if check_service "后台API" "$API_BASE_URL/quiz/list"; then
        ((passed_tests++))
    fi
    
    # 检查管理后台
    ((total_tests++))
    if check_service "管理后台" "$ADMIN_URL"; then
        ((passed_tests++))
    fi
    
    # API功能测试
    ((total_tests++))
    if test_user_registration; then
        ((passed_tests++))
    fi
    
    ((total_tests++))
    if test_user_login; then
        ((passed_tests++))
    fi
    
    ((total_tests++))
    if test_quiz_list; then
        ((passed_tests++))
    fi
    
    ((total_tests++))
    if test_quiz_detail; then
        ((passed_tests++))
    fi
    
    ((total_tests++))
    if test_quiz_questions; then
        ((passed_tests++))
    fi
    
    ((total_tests++))
    if test_quiz_submission; then
        ((passed_tests++))
    fi
    
    # 生成报告
    generate_report $total_tests $passed_tests
    
    # 清理临时文件
    rm -f /tmp/quiz_test_user.txt /tmp/quiz_test_token.txt
}

# 检查参数
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "QUIZ心理测评系统测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  --api-url URL  指定API基础URL (默认: $API_BASE_URL)"
    echo "  --admin-url URL 指定管理后台URL (默认: $ADMIN_URL)"
    echo ""
    echo "示例:"
    echo "  $0"
    echo "  $0 --api-url http://192.168.1.100:8080/quiz-api/api"
    exit 0
fi

# 解析参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --api-url)
            API_BASE_URL="$2"
            shift 2
            ;;
        --admin-url)
            ADMIN_URL="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# 运行主程序
main
