-- 测试数据库连接和基础数据
USE quiz;

-- 检查表是否创建成功
SHOW TABLES;

-- 检查用户表结构
DESCRIBE users;

-- 检查测评分类数据
SELECT * FROM quiz_categories;

-- 检查系统配置
SELECT * FROM system_configs;

-- 插入测试用户
INSERT INTO users (username, email, password_hash, nickname, gender) VALUES
('testuser', '<EMAIL>', 'hashed_password_here', '测试用户', 1);

-- 插入测试测评
INSERT INTO quizzes (title, subtitle, description, category_id, question_count, estimated_time, is_featured) VALUES
('MBTI人格测试', '16型人格测评', '通过科学的心理学测评，了解你的人格类型', 1, 60, 15, TRUE),
('情商测试', 'EQ情绪智力测评', '测试你的情绪管理和社交能力', 2, 40, 10, TRUE);

-- 验证数据插入
SELECT u.nickname, q.title, c.name as category_name 
FROM users u, quizzes q, quiz_categories c 
WHERE q.category_id = c.id;
