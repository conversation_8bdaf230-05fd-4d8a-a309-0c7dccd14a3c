# QUIZ 数据库设计文档

## 数据库配置
- **数据库名**: quiz
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **用户**: root
- **密码**: SD0916sd!

## 数据表结构

### 1. 用户相关表

#### users (用户表)
| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | BIGINT | 主键，自增 | PRIMARY |
| username | VARCHAR(50) | 用户名，唯一 | UNIQUE |
| email | VARCHAR(100) | 邮箱，唯一 | UNIQUE |
| phone | VARCHAR(20) | 手机号，唯一 | UNIQUE |
| password_hash | VARCHAR(255) | 密码哈希 | - |
| nickname | VARCHAR(50) | 昵称 | - |
| avatar_url | VARCHAR(500) | 头像URL | - |
| gender | TINYINT | 性别：0-未知,1-男,2-女 | - |
| birth_date | DATE | 出生日期 | - |
| is_vip | BOOLEAN | 是否VIP会员 | - |
| vip_expire_time | DATETIME | VIP过期时间 | - |
| status | TINYINT | 状态：0-禁用,1-正常 | - |

#### user_favorites (用户收藏表)
| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | BIGINT | 主键，自增 | PRIMARY |
| user_id | BIGINT | 用户ID | INDEX |
| quiz_id | BIGINT | 测评ID | INDEX |
| created_at | TIMESTAMP | 创建时间 | - |

### 2. 测评内容表

#### quiz_categories (测评分类表)
| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | INT | 主键，自增 | PRIMARY |
| name | VARCHAR(50) | 分类名称 | - |
| description | TEXT | 分类描述 | - |
| icon_url | VARCHAR(500) | 分类图标 | - |
| sort_order | INT | 排序 | - |
| status | TINYINT | 状态：0-禁用,1-启用 | - |

#### quizzes (测评表)
| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | BIGINT | 主键，自增 | PRIMARY |
| title | VARCHAR(200) | 测评标题 | - |
| subtitle | VARCHAR(500) | 副标题 | - |
| description | TEXT | 测评描述 | - |
| category_id | INT | 分类ID | INDEX |
| cover_image | VARCHAR(500) | 封面图片 | - |
| question_count | INT | 题目数量 | - |
| estimated_time | INT | 预估时间(分钟) | - |
| difficulty_level | TINYINT | 难度：1-简单,2-中等,3-困难 | - |
| is_free | BOOLEAN | 是否免费 | - |
| price | DECIMAL(10,2) | 价格 | - |
| is_featured | BOOLEAN | 是否推荐 | INDEX |
| view_count | BIGINT | 浏览次数 | - |
| take_count | BIGINT | 参与次数 | - |
| status | TINYINT | 状态：0-下架,1-上架 | INDEX |

#### questions (题目表)
| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | BIGINT | 主键，自增 | PRIMARY |
| quiz_id | BIGINT | 测评ID | INDEX |
| question_text | TEXT | 题目内容 | - |
| question_type | TINYINT | 类型：1-单选,2-多选,3-滑动打分 | - |
| image_url | VARCHAR(500) | 题目图片 | - |
| sort_order | INT | 排序 | INDEX |
| is_required | BOOLEAN | 是否必答 | - |

#### question_options (题目选项表)
| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | BIGINT | 主键，自增 | PRIMARY |
| question_id | BIGINT | 题目ID | INDEX |
| option_text | TEXT | 选项内容 | - |
| option_value | VARCHAR(50) | 选项值 | - |
| score | INT | 选项分数 | - |
| sort_order | INT | 排序 | INDEX |

### 3. 测评结果表

#### quiz_result_templates (测评结果模板表)
| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | BIGINT | 主键，自增 | PRIMARY |
| quiz_id | BIGINT | 测评ID | INDEX |
| result_type | VARCHAR(50) | 结果类型标识 | INDEX |
| title | VARCHAR(200) | 结果标题 | - |
| description | TEXT | 结果描述 | - |
| detailed_analysis | TEXT | 详细分析 | - |
| suggestions | TEXT | 建议 | - |
| min_score | INT | 最低分数 | - |
| max_score | INT | 最高分数 | - |
| image_url | VARCHAR(500) | 结果图片 | - |

### 4. 用户答题记录表

#### user_quiz_records (用户测评记录表)
| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | BIGINT | 主键，自增 | PRIMARY |
| user_id | BIGINT | 用户ID | INDEX |
| quiz_id | BIGINT | 测评ID | INDEX |
| start_time | TIMESTAMP | 开始时间 | - |
| end_time | TIMESTAMP | 结束时间 | - |
| total_score | INT | 总分 | - |
| result_type | VARCHAR(50) | 结果类型 | - |
| status | TINYINT | 状态：0-进行中,1-已完成,2-已放弃 | INDEX |

#### user_answers (用户答案表)
| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | BIGINT | 主键，自增 | PRIMARY |
| record_id | BIGINT | 测评记录ID | INDEX |
| question_id | BIGINT | 题目ID | INDEX |
| option_id | BIGINT | 选项ID | - |
| answer_text | TEXT | 答案文本 | - |
| score | INT | 得分 | - |

### 5. 系统配置表

#### system_configs (系统配置表)
| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | INT | 主键，自增 | PRIMARY |
| config_key | VARCHAR(100) | 配置键，唯一 | UNIQUE |
| config_value | TEXT | 配置值 | - |
| description | VARCHAR(500) | 配置描述 | - |

## 初始数据

### 测评分类
- 人格测试 (MBTI、DISC等)
- 情商测试 (情绪智力测评)
- 职业测试 (职业兴趣测评)
- 恋爱测试 (恋爱观测评)
- 心理健康 (心理状态测评)

### 系统配置
- 应用名称和版本
- VIP价格设置
- 免费测试次数限制

## 使用说明

1. 执行 `init.sql` 创建数据库和表结构
2. 执行 `test_connection.sql` 测试连接和插入测试数据
3. 根据需要调整配置参数
