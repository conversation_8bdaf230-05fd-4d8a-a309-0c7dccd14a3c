-- QUIZ 心理测评系统数据库初始化脚本
-- 数据库名称: quiz
-- 用户: root
-- 密码: SD0916sd!

-- 创建数据库
CREATE DATABASE IF NOT EXISTS quiz CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE quiz;

-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) UNIQUE COMMENT '手机号',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    gender TINYINT COMMENT '性别: 0-未知, 1-男, 2-女',
    birth_date DATE COMMENT '出生日期',
    is_vip BOOLEAN DEFAULT FALSE COMMENT '是否VIP会员',
    vip_expire_time DATETIME COMMENT 'VIP过期时间',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用, 1-正常',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone)
) COMMENT '用户表';

-- 测评分类表
CREATE TABLE quiz_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '分类名称',
    description TEXT COMMENT '分类描述',
    icon_url VARCHAR(500) COMMENT '分类图标',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '测评分类表';

-- 测评表
CREATE TABLE quizzes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL COMMENT '测评标题',
    subtitle VARCHAR(500) COMMENT '副标题',
    description TEXT COMMENT '测评描述',
    category_id INT COMMENT '分类ID',
    cover_image VARCHAR(500) COMMENT '封面图片',
    question_count INT DEFAULT 0 COMMENT '题目数量',
    estimated_time INT DEFAULT 0 COMMENT '预估完成时间(分钟)',
    difficulty_level TINYINT DEFAULT 1 COMMENT '难度等级: 1-简单, 2-中等, 3-困难',
    is_free BOOLEAN DEFAULT TRUE COMMENT '是否免费',
    price DECIMAL(10,2) DEFAULT 0.00 COMMENT '价格',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
    view_count BIGINT DEFAULT 0 COMMENT '浏览次数',
    take_count BIGINT DEFAULT 0 COMMENT '参与次数',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-下架, 1-上架',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES quiz_categories(id),
    INDEX idx_category (category_id),
    INDEX idx_status (status),
    INDEX idx_featured (is_featured)
) COMMENT '测评表';

-- 题目表
CREATE TABLE questions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    quiz_id BIGINT NOT NULL COMMENT '测评ID',
    question_text TEXT NOT NULL COMMENT '题目内容',
    question_type TINYINT DEFAULT 1 COMMENT '题目类型: 1-单选, 2-多选, 3-滑动打分',
    image_url VARCHAR(500) COMMENT '题目图片',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_required BOOLEAN DEFAULT TRUE COMMENT '是否必答',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (quiz_id) REFERENCES quizzes(id) ON DELETE CASCADE,
    INDEX idx_quiz (quiz_id),
    INDEX idx_sort (sort_order)
) COMMENT '题目表';

-- 题目选项表
CREATE TABLE question_options (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    question_id BIGINT NOT NULL COMMENT '题目ID',
    option_text TEXT NOT NULL COMMENT '选项内容',
    option_value VARCHAR(50) COMMENT '选项值',
    score INT DEFAULT 0 COMMENT '选项分数',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,
    INDEX idx_question (question_id),
    INDEX idx_sort (sort_order)
) COMMENT '题目选项表';

-- 测评结果模板表
CREATE TABLE quiz_result_templates (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    quiz_id BIGINT NOT NULL COMMENT '测评ID',
    result_type VARCHAR(50) NOT NULL COMMENT '结果类型标识',
    title VARCHAR(200) NOT NULL COMMENT '结果标题',
    description TEXT COMMENT '结果描述',
    detailed_analysis TEXT COMMENT '详细分析',
    suggestions TEXT COMMENT '建议',
    min_score INT COMMENT '最低分数',
    max_score INT COMMENT '最高分数',
    image_url VARCHAR(500) COMMENT '结果图片',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (quiz_id) REFERENCES quizzes(id) ON DELETE CASCADE,
    INDEX idx_quiz (quiz_id),
    INDEX idx_type (result_type)
) COMMENT '测评结果模板表';

-- 用户测评记录表
CREATE TABLE user_quiz_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    quiz_id BIGINT NOT NULL COMMENT '测评ID',
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    total_score INT DEFAULT 0 COMMENT '总分',
    result_type VARCHAR(50) COMMENT '结果类型',
    status TINYINT DEFAULT 0 COMMENT '状态: 0-进行中, 1-已完成, 2-已放弃',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (quiz_id) REFERENCES quizzes(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_quiz (quiz_id),
    INDEX idx_status (status),
    INDEX idx_created (created_at)
) COMMENT '用户测评记录表';

-- 用户答案表
CREATE TABLE user_answers (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    record_id BIGINT NOT NULL COMMENT '测评记录ID',
    question_id BIGINT NOT NULL COMMENT '题目ID',
    option_id BIGINT COMMENT '选项ID(单选/多选)',
    answer_text TEXT COMMENT '答案文本',
    score INT DEFAULT 0 COMMENT '得分',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (record_id) REFERENCES user_quiz_records(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,
    FOREIGN KEY (option_id) REFERENCES question_options(id) ON DELETE CASCADE,
    INDEX idx_record (record_id),
    INDEX idx_question (question_id)
) COMMENT '用户答案表';

-- 系统配置表
CREATE TABLE system_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(500) COMMENT '配置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_key (config_key)
) COMMENT '系统配置表';

-- 用户收藏表
CREATE TABLE user_favorites (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    quiz_id BIGINT NOT NULL COMMENT '测评ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (quiz_id) REFERENCES quizzes(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_quiz (user_id, quiz_id),
    INDEX idx_user (user_id),
    INDEX idx_quiz (quiz_id)
) COMMENT '用户收藏表';

-- 插入初始数据
INSERT INTO quiz_categories (name, description, icon_url, sort_order) VALUES
('人格测试', 'MBTI、DISC等经典人格测评', '/icons/personality.png', 1),
('情商测试', '情绪智力和社交能力测评', '/icons/eq.png', 2),
('职业测试', '职业兴趣和能力倾向测评', '/icons/career.png', 3),
('恋爱测试', '恋爱观和情感能力测评', '/icons/love.png', 4),
('心理健康', '心理状态和健康水平测评', '/icons/mental.png', 5);

-- 插入系统配置
INSERT INTO system_configs (config_key, config_value, description) VALUES
('app_name', 'QUIZ心理测评', '应用名称'),
('app_version', '1.0.0', '应用版本'),
('vip_price_monthly', '19.9', 'VIP月费价格'),
('vip_price_yearly', '199.9', 'VIP年费价格'),
('max_free_tests_per_day', '3', '每日免费测试次数限制');
